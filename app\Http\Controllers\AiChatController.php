<?php

namespace App\Http\Controllers;

use App\Models\AiConversation;
use App\Models\AiMessage;
use App\Services\GeminiService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AiChatController extends Controller
{
    /**
     * Get or create a conversation for the current user/session.
     */
    public function getConversation(Request $request)
    {
        if (Auth::check()) {
            // For authenticated users
            $conversation = AiConversation::where('user_id', Auth::id())
                ->latest()
                ->first();

            if (!$conversation) {
                $conversation = AiConversation::create([
                    'user_id' => Auth::id(),
                    'title' => 'New Conversation',
                ]);
            }
        } else {
            // For non-authenticated users, use session ID
            $sessionId = $request->session()->get('ai_chat_session_id');

            if (!$sessionId) {
                $sessionId = Str::uuid()->toString();
                $request->session()->put('ai_chat_session_id', $sessionId);
            }

            $conversation = AiConversation::where('session_id', $sessionId)
                ->latest()
                ->first();

            if (!$conversation) {
                $conversation = AiConversation::create([
                    'session_id' => $sessionId,
                    'title' => 'New Conversation',
                ]);
            }
        }

        // Load the messages for this conversation
        $messages = $conversation->messages()->orderBy('created_at')->get();

        // If there are no messages, add a welcome message
        if ($messages->isEmpty()) {
            // Create a welcome message using Gemini
            $geminiService = new GeminiService();
            $welcomeMessage = $geminiService->generateResponse("You are an AI assistant for Digitora, a digital marketplace. Introduce yourself briefly and ask how you can help the user today.");

            $welcomeMsg = AiMessage::create([
                'conversation_id' => $conversation->id,
                'sender_type' => 'ai',
                'content' => $welcomeMessage,
            ]);

            $messages = collect([$welcomeMsg]);
        }

        return response()->json([
            'conversation' => $conversation,
            'messages' => $messages,
            'is_authenticated' => Auth::check(),
        ]);
    }

    /**
     * Send a message to the AI and get a response.
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|uuid|exists:ai_conversations,id',
            'message' => 'required|string',
            'page_context' => 'sometimes|array',
        ]);

        $conversation = AiConversation::findOrFail($request->conversation_id);

        // Check if the user is authorized to access this conversation
        if (Auth::check()) {
            if ($conversation->user_id && $conversation->user_id !== Auth::id()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
        } else {
            $sessionId = $request->session()->get('ai_chat_session_id');
            if ($conversation->session_id !== $sessionId) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
        }

        // Save the user's message
        $userMessage = AiMessage::create([
            'conversation_id' => $conversation->id,
            'sender_type' => 'user',
            'content' => $request->message,
        ]);

        // Get previous messages for context (last 10 messages)
        $previousMessages = $conversation->messages()
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get()
            ->sortBy('created_at')
            ->toArray();

        // Get page context if provided
        $pageContext = $request->input('page_context', []);

        // Debug: Log the received page context
        Log::info('AI Chat - Received page context:', $pageContext);

        // Generate AI response using Gemini with page context
        $geminiService = new GeminiService();
        $aiResponse = $geminiService->generateResponse($request->message, $previousMessages, $pageContext);

        // Save the AI's response
        $aiMessage = AiMessage::create([
            'conversation_id' => $conversation->id,
            'sender_type' => 'ai',
            'content' => $aiResponse,
        ]);

        return response()->json([
            'user_message' => $userMessage,
            'ai_message' => $aiMessage,
        ]);
    }

    /**
     * Clear the conversation history.
     */
    public function clearConversation(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|uuid|exists:ai_conversations,id',
        ]);

        $conversation = AiConversation::findOrFail($request->conversation_id);

        // Check if the user is authorized to access this conversation
        if (Auth::check()) {
            if ($conversation->user_id && $conversation->user_id !== Auth::id()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
        } else {
            $sessionId = $request->session()->get('ai_chat_session_id');
            if ($conversation->session_id !== $sessionId) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
        }

        // Delete all messages in the conversation
        $conversation->messages()->delete();

        // Create a welcome message using Gemini
        $geminiService = new GeminiService();
        $welcomeMessage = $geminiService->generateResponse("You are an AI assistant for Digitora, a digital marketplace. Introduce yourself briefly and ask how you can help the user today.");

        AiMessage::create([
            'conversation_id' => $conversation->id,
            'sender_type' => 'ai',
            'content' => $welcomeMessage,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Conversation cleared successfully',
        ]);
    }
}
