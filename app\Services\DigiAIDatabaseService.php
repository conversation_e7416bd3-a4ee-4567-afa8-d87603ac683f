<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductDetailedCategory;
use App\Models\SellerApplication;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class DigiAIDatabaseService
{
    /**
     * Search for products based on user query
     */
    public function searchProducts(string $query, int $limit = 5): array
    {
        $searchTerms = explode(' ', strtolower($query));

        $products = Product::where('status', 'active')
            ->whereHas('seller', function($q) {
                $q->whereHas('sellerApplication', function($q2) {
                    $q2->where('status', 'approved');
                });
            })
            ->where(function($q) use ($searchTerms) {
                foreach ($searchTerms as $term) {
                    $q->where(function($subQ) use ($term) {
                        $subQ->where('name', 'like', "%{$term}%")
                             ->orWhere('description', 'like', "%{$term}%")
                             ->orWhere('category', 'like', "%{$term}%");
                    });
                }
            })
            ->with([
                'seller.sellerApplication',
                'productCategory',
                'productSubcategory',
                'productDetailedCategory'
            ])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return $products->map(function($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'price' => $product->price,
                'formatted_price' => 'Rp ' . number_format($product->price, 0, ',', '.'),
                'discount_price' => $product->discount_price,
                'formatted_discount_price' => $product->discount_price ? 'Rp ' . number_format($product->discount_price, 0, ',', '.') : null,
                'category_name' => $product->category_name,
                'subcategory_name' => $product->subcategory_name,
                'detailed_category_name' => $product->detailed_category_name,
                'store_name' => $product->seller->sellerApplication->store_name ?? 'Unknown Store',
                'store_slug' => $product->seller->sellerApplication->store_name_slug ?? null,
                'product_url' => $this->generateProductUrl($product),
                'store_url' => $this->generateStoreUrl($product->seller->sellerApplication),
                'description_excerpt' => Str::limit($product->description, 100)
            ];
        })->toArray();
    }

    /**
     * Get products by category
     */
    public function getProductsByCategory(string $categorySlug, int $limit = 5): array
    {
        // Try to find by new category structure first
        $category = ProductCategory::where('slug', $categorySlug)->first();

        if ($category) {
            $products = Product::where('status', 'active')
                ->where('category_id', $category->id)
                ->whereHas('seller', function($q) {
                    $q->whereHas('sellerApplication', function($q2) {
                        $q2->where('status', 'approved');
                    });
                })
                ->with([
                    'seller.sellerApplication',
                    'productCategory',
                    'productSubcategory',
                    'productDetailedCategory'
                ])
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
        } else {
            // Fallback to legacy category field
            $products = Product::where('status', 'active')
                ->where('category', 'like', "%{$categorySlug}%")
                ->whereHas('seller', function($q) {
                    $q->whereHas('sellerApplication', function($q2) {
                        $q2->where('status', 'approved');
                    });
                })
                ->with(['seller.sellerApplication'])
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
        }

        return $products->map(function($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'price' => $product->price,
                'formatted_price' => 'Rp ' . number_format($product->price, 0, ',', '.'),
                'discount_price' => $product->discount_price,
                'formatted_discount_price' => $product->discount_price ? 'Rp ' . number_format($product->discount_price, 0, ',', '.') : null,
                'category_name' => $product->category_name,
                'store_name' => $product->seller->sellerApplication->store_name ?? 'Unknown Store',
                'store_slug' => $product->seller->sellerApplication->store_name_slug ?? null,
                'product_url' => $this->generateProductUrl($product),
                'store_url' => $this->generateStoreUrl($product->seller->sellerApplication)
            ];
        })->toArray();
    }

    /**
     * Get all active categories
     */
    public function getCategories(): array
    {
        $categories = ProductCategory::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        return $categories->map(function($category) {
            $productCount = Product::where('status', 'active')
                ->where('category_id', $category->id)
                ->whereHas('seller', function($q) {
                    $q->whereHas('sellerApplication', function($q2) {
                        $q2->where('status', 'approved');
                    });
                })
                ->count();

            return [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'description' => $category->description,
                'product_count' => $productCount,
                'browse_url' => route('user.browse', ['category' => $category->slug])
            ];
        })->toArray();
    }

    /**
     * Get trending/popular products
     */
    public function getTrendingProducts(int $limit = 5): array
    {
        $products = Product::where('status', 'active')
            ->whereHas('seller', function($q) {
                $q->whereHas('sellerApplication', function($q2) {
                    $q2->where('status', 'approved');
                });
            })
            ->with([
                'seller.sellerApplication',
                'productCategory',
                'productSubcategory',
                'productDetailedCategory'
            ])
            ->orderBy('reviews_count', 'desc')
            ->orderBy('average_rating', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return $products->map(function($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'price' => $product->price,
                'formatted_price' => 'Rp ' . number_format($product->price, 0, ',', '.'),
                'category_name' => $product->category_name,
                'store_name' => $product->seller->sellerApplication->store_name ?? 'Unknown Store',
                'store_slug' => $product->seller->sellerApplication->store_name_slug ?? null,
                'product_url' => $this->generateProductUrl($product),
                'store_url' => $this->generateStoreUrl($product->seller->sellerApplication),
                'average_rating' => $product->average_rating,
                'reviews_count' => $product->reviews_count
            ];
        })->toArray();
    }

    /**
     * Get price range for products
     */
    public function getPriceRange(): array
    {
        $priceStats = Product::where('status', 'active')
            ->whereHas('seller', function($q) {
                $q->whereHas('sellerApplication', function($q2) {
                    $q2->where('status', 'approved');
                });
            })
            ->selectRaw('MIN(price) as min_price, MAX(price) as max_price, AVG(price) as avg_price')
            ->first();

        return [
            'min_price' => $priceStats->min_price ?? 0,
            'max_price' => $priceStats->max_price ?? 0,
            'avg_price' => round($priceStats->avg_price ?? 0),
            'formatted_min' => 'Rp ' . number_format($priceStats->min_price ?? 0, 0, ',', '.'),
            'formatted_max' => 'Rp ' . number_format($priceStats->max_price ?? 0, 0, ',', '.'),
            'formatted_avg' => 'Rp ' . number_format(round($priceStats->avg_price ?? 0), 0, ',', '.')
        ];
    }

    /**
     * Get store information
     */
    public function getStoreInfo(string $storeSlug): ?array
    {
        $sellerApplication = SellerApplication::where('store_name_slug', $storeSlug)
            ->where('status', 'approved')
            ->with('user')
            ->first();

        if (!$sellerApplication) {
            return null;
        }

        $productCount = Product::where('seller_id', $sellerApplication->user_id)
            ->where('status', 'active')
            ->count();

        return [
            'store_name' => $sellerApplication->store_name,
            'store_slug' => $sellerApplication->store_name_slug,
            'store_description' => $sellerApplication->store_description,
            'product_count' => $productCount,
            'store_url' => route('store.show', $sellerApplication->store_name_slug)
        ];
    }

    /**
     * Generate product URL
     */
    private function generateProductUrl(Product $product): string
    {
        if ($product->seller && $product->seller->sellerApplication && $product->seller->sellerApplication->store_name_slug) {
            return route('store.product', [
                'sellerApplication' => $product->seller->sellerApplication->store_name_slug,
                'product' => $product->slug
            ]);
        }

        return route('product', $product->slug);
    }

    /**
     * Generate store URL
     */
    private function generateStoreUrl(?SellerApplication $sellerApplication): ?string
    {
        if ($sellerApplication && $sellerApplication->store_name_slug) {
            return route('store.show', $sellerApplication->store_name_slug);
        }

        return null;
    }

    /**
     * Get product statistics for sellers
     */
    public function getProductStats(): array
    {
        $totalProducts = Product::where('status', 'active')->count();
        $totalStores = SellerApplication::where('status', 'approved')->count();
        $totalCategories = ProductCategory::where('is_active', true)->count();

        return [
            'total_products' => $totalProducts,
            'total_stores' => $totalStores,
            'total_categories' => $totalCategories
        ];
    }

    /**
     * Get detailed product information
     */
    public function getProductDetails(string $productId): ?array
    {
        try {
            $product = DB::table('products')
                ->leftJoin('seller_applications', 'products.seller_id', '=', 'seller_applications.user_id')
                ->leftJoin('product_detailed_categories', 'products.detailed_category_id', '=', 'product_detailed_categories.id')
                ->leftJoin('product_subcategories', 'product_detailed_categories.subcategory_id', '=', 'product_subcategories.id')
                ->leftJoin('product_categories', 'product_subcategories.category_id', '=', 'product_categories.id')
                ->where('products.id', $productId)
                ->where('products.status', 'active')
                ->select([
                    'products.id',
                    'products.name',
                    'products.description',
                    'products.price',
                    'products.slug',
                    'products.category as legacy_category',
                    'seller_applications.store_name',
                    'seller_applications.store_name_slug',
                    'product_detailed_categories.name as detailed_category_name',
                    'product_subcategories.name as subcategory_name',
                    'product_categories.name as category_name'
                ])
                ->first();

            if (!$product) {
                return null;
            }

            return [
                'id' => $product->id,
                'name' => $product->name,
                'description' => $product->description,
                'price' => $product->price,
                'formatted_price' => 'Rp ' . number_format($product->price, 0, ',', '.'),
                'slug' => $product->slug,
                'store_name' => $product->store_name ?? 'Unknown Store',
                'store_slug' => $product->store_name_slug ?? '',
                'category_name' => $product->category_name ?? ucfirst($product->legacy_category ?? 'Uncategorized'),
                'subcategory_name' => $product->subcategory_name,
                'detailed_category_name' => $product->detailed_category_name
            ];
        } catch (\Exception $e) {
            Log::error('Error getting product details: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get detailed store information
     */
    public function getStoreDetails(string $storeSlug): ?array
    {
        try {
            $store = DB::table('seller_applications')
                ->where('store_name_slug', $storeSlug)
                ->where('status', 'approved')
                ->first();

            if (!$store) {
                return null;
            }

            // Get store product count and categories
            $productStats = DB::table('products')
                ->leftJoin('product_detailed_categories', 'products.detailed_category_id', '=', 'product_detailed_categories.id')
                ->leftJoin('product_subcategories', 'product_detailed_categories.subcategory_id', '=', 'product_subcategories.id')
                ->leftJoin('product_categories', 'product_subcategories.category_id', '=', 'product_categories.id')
                ->where('products.seller_id', $store->user_id)
                ->where('products.status', 'active')
                ->select([
                    DB::raw('COALESCE(product_categories.name, products.category, "Uncategorized") as category_name'),
                    DB::raw('COUNT(*) as product_count')
                ])
                ->groupBy(DB::raw('COALESCE(product_categories.name, products.category, "Uncategorized")'))
                ->get();

            $totalProducts = $productStats->sum('product_count');
            $categories = $productStats->pluck('category_name')->toArray();

            return [
                'id' => $store->id,
                'store_name' => $store->store_name,
                'store_name_slug' => $store->store_name_slug,
                'about_store' => $store->about_store,
                'total_products' => $totalProducts,
                'categories' => $categories
            ];
        } catch (\Exception $e) {
            Log::error('Error getting store details: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get detailed category information
     */
    public function getCategoryDetails(string $categorySlug): ?array
    {
        try {
            // Try to find in main categories first
            $category = DB::table('product_categories')
                ->where('slug', $categorySlug)
                ->first();

            if ($category) {
                $productCount = DB::table('products')
                    ->leftJoin('product_detailed_categories', 'products.detailed_category_id', '=', 'product_detailed_categories.id')
                    ->leftJoin('product_subcategories', 'product_detailed_categories.subcategory_id', '=', 'product_subcategories.id')
                    ->where('product_subcategories.category_id', $category->id)
                    ->where('products.status', 'active')
                    ->count();

                $subcategories = DB::table('product_subcategories')
                    ->where('category_id', $category->id)
                    ->pluck('name')
                    ->toArray();

                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'product_count' => $productCount,
                    'subcategories' => $subcategories,
                    'type' => 'category'
                ];
            }

            // Try subcategories
            $subcategory = DB::table('product_subcategories')
                ->join('product_categories', 'product_subcategories.category_id', '=', 'product_categories.id')
                ->where('product_subcategories.slug', $categorySlug)
                ->select([
                    'product_subcategories.id',
                    'product_subcategories.name',
                    'product_subcategories.slug',
                    'product_categories.name as parent_category'
                ])
                ->first();

            if ($subcategory) {
                $productCount = DB::table('products')
                    ->leftJoin('product_detailed_categories', 'products.detailed_category_id', '=', 'product_detailed_categories.id')
                    ->where('product_detailed_categories.subcategory_id', $subcategory->id)
                    ->where('products.status', 'active')
                    ->count();

                return [
                    'id' => $subcategory->id,
                    'name' => $subcategory->name,
                    'slug' => $subcategory->slug,
                    'parent_category' => $subcategory->parent_category,
                    'product_count' => $productCount,
                    'type' => 'subcategory'
                ];
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Error getting category details: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get seller statistics
     */
    public function getSellerStats(string $sellerId): array
    {
        try {
            // Get seller application to find user_id
            $sellerApp = DB::table('seller_applications')->where('id', $sellerId)->first();
            if (!$sellerApp) {
                return [
                    'total_products' => 0,
                    'total_revenue' => 0,
                    'formatted_revenue' => 'Rp 0',
                    'total_orders' => 0
                ];
            }

            $totalProducts = DB::table('products')
                ->where('seller_id', $sellerApp->user_id)
                ->where('status', 'active')
                ->count();

            $revenueData = DB::table('orders')
                ->join('products', 'orders.product_id', '=', 'products.id')
                ->where('products.seller_id', $sellerApp->user_id)
                ->where('orders.status', 'success')
                ->select([
                    DB::raw('SUM(orders.amount) as total_revenue'),
                    DB::raw('COUNT(DISTINCT orders.id) as total_orders')
                ])
                ->first();

            $totalRevenue = $revenueData->total_revenue ?? 0;
            $totalOrders = $revenueData->total_orders ?? 0;

            return [
                'total_products' => $totalProducts,
                'total_revenue' => $totalRevenue,
                'formatted_revenue' => 'Rp ' . number_format($totalRevenue, 0, ',', '.'),
                'total_orders' => $totalOrders
            ];
        } catch (\Exception $e) {
            Log::error('Error getting seller stats: ' . $e->getMessage());
            return [
                'total_products' => 0,
                'total_revenue' => 0,
                'formatted_revenue' => 'Rp 0',
                'total_orders' => 0
            ];
        }
    }

    /**
     * Get user statistics
     */
    public function getUserStats(string $userId): array
    {
        try {
            $purchaseData = DB::table('orders')
                ->where('orders.user_id', $userId)
                ->where('orders.status', 'success')
                ->select([
                    DB::raw('SUM(orders.amount) as total_spent'),
                    DB::raw('COUNT(DISTINCT orders.id) as total_purchases')
                ])
                ->first();

            $totalSpent = $purchaseData->total_spent ?? 0;
            $totalPurchases = $purchaseData->total_purchases ?? 0;

            return [
                'total_purchases' => $totalPurchases,
                'total_spent' => $totalSpent,
                'formatted_spent' => 'Rp ' . number_format($totalSpent, 0, ',', '.')
            ];
        } catch (\Exception $e) {
            Log::error('Error getting user stats: ' . $e->getMessage());
            return [
                'total_purchases' => 0,
                'total_spent' => 0,
                'formatted_spent' => 'Rp 0'
            ];
        }
    }
}
