<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeminiService
{
    protected $apiKey;
    protected $model;
    protected $baseUrl;
    protected $databaseService;

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key');
        $this->model = config('services.gemini.model');
        $this->baseUrl = "https://generativelanguage.googleapis.com/v1beta/models/{$this->model}:generateContent";
        $this->databaseService = new DigiAIDatabaseService();
    }

    /**
     * Generate a response from Gemini AI with DigiAI personality
     *
     * @param string $prompt The user's message
     * @param array $history Optional conversation history
     * @return string The AI response
     */
    public function generateResponse(string $prompt, array $history = []): string
    {
        try {
            // Determine if this is a welcome message or regular conversation
            $isWelcomeMessage = $this->isWelcomeMessage($prompt);

            // Prepare the conversation history in the format Gemini expects
            $contents = [];

            // Add DigiAI system prompt
            $systemPrompt = $this->getDigiAISystemPrompt($isWelcomeMessage);

            // Add conversation history if provided
            if (!empty($history)) {
                foreach ($history as $message) {
                    $role = $message['sender_type'] === 'user' ? 'user' : 'model';
                    $contents[] = [
                        'role' => $role,
                        'parts' => [
                            ['text' => $message['content']]
                        ]
                    ];
                }
            }

            // Add the current prompt with context
            $contextualPrompt = $isWelcomeMessage ? $systemPrompt : $this->addDigiAIContext($prompt, $history);
            $contents[] = [
                'role' => 'user',
                'parts' => [
                    ['text' => $contextualPrompt]
                ]
            ];

            // Make the API request
            $response = Http::post("{$this->baseUrl}?key={$this->apiKey}", [
                'contents' => $contents,
                'generationConfig' => [
                    'temperature' => 0.7,
                    'topK' => 40,
                    'topP' => 0.95,
                    'maxOutputTokens' => 1024,
                ],
            ]);

            // Check if the request was successful
            if ($response->successful()) {
                $data = $response->json();

                // Extract the response text
                if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                    return $data['candidates'][0]['content']['parts'][0]['text'];
                }

                // If the response format is unexpected, return a fallback message
                Log::warning('Unexpected Gemini API response format', ['response' => $data]);
                return "I'm sorry, I couldn't generate a proper response at the moment. Please try again later.";
            } else {
                // Log the error
                Log::error('Gemini API error', [
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);

                return "I'm sorry, there was an error processing your request. Please try again later.";
            }
        } catch (\Exception $e) {
            // Log any exceptions
            Log::error('Exception in Gemini service', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return "I'm sorry, an unexpected error occurred. Please try again later.";
        }
    }

    /**
     * Check if this is a welcome message request
     */
    private function isWelcomeMessage(string $prompt): bool
    {
        return strpos($prompt, 'You are an AI assistant for Digitora') !== false ||
               strpos($prompt, 'Introduce yourself') !== false;
    }

    /**
     * Get the DigiAI system prompt based on context
     */
    private function getDigiAISystemPrompt(bool $isWelcomeMessage): string
    {
        if ($isWelcomeMessage) {
            return "You are DigiAI, the intelligent assistant for Digitora, an Indonesian digital marketplace. Start the conversation with: 'Halo! Saya DigiAI, asisten cerdas Anda di Digitora. Mari kita mulai—pilih bahasa Anda: ketik \"Bahasa Indonesia\" atau \"English\"!' Always be friendly, helpful, and engaging. Use Indonesian as default but switch to English if requested.";
        }

        return "You are DigiAI, the intelligent assistant for Digitora, an Indonesian digital marketplace.

PERSONALITY:
- Friendly, engaging, and helpful
- Use Indonesian as default unless user prefers English
- Adapt tone: friendly for buyers, professional for sellers
- Avoid technical jargon unless requested
- Keep responses concise yet helpful - like a chat, not too long
- Never promote illegal activities or external platforms

INTERACTION FLOW:
1. Language Selection: Ask users to choose 'Bahasa Indonesia' or 'English'
2. Role Identification: Ask if they want to 'Beli' (buy) or 'Jual' (sell)
3. Provide tailored assistance based on their role

FOR BUYERS:
- Start conversational: 'Apa yang sedang Anda cari hari ini?'
- Build curiosity: 'Untuk keperluan apa nih? Kerja, bisnis, atau pribadi?'
- Create excitement: 'Wah menarik! Digitora punya solusi yang tepat untuk itu'
- Focus on benefits: 'Bayangin kalau pekerjaan jadi lebih cepat dan rapi'
- Only give specific examples if user asks for recommendations or seems confused

FOR SELLERS:
- Start motivational: 'Siap jadi kreator sukses di Digitora?'
- Ask about skills: 'Apa keahlian atau karya yang ingin Anda bagikan?'
- Build confidence: 'Skill seperti itu sangat dibutuhkan banyak orang!'
- Create urgency: 'Saatnya monetize keahlian Anda'
- Only provide market data if seller specifically asks for insights

ENGAGEMENT PROMPTS:
- If user seems unsure: 'Bingung? Coba ceritakan apa yang Anda cari, saya akan bantu!'
- General: 'Apa yang paling Anda butuhkan dari Digitora hari ini?'
- Follow-up with relevant suggestions and tips

RESTRICTIONS & GUIDELINES:
- Do not provide external links or promote other platforms
- Ensure all advice aligns with Digitora's mission to empower local creators
- If unsure, ask clarifying questions
- Never assist with illegal activities or platform misuse

CULTURAL & ETHICAL SENSITIVITY:
- Respect Indonesia's diverse cultural and religious backgrounds
- Avoid religious references, political statements, or controversial topics
- Use polite, respectful language appropriate for Indonesian culture
- No offensive slang or insults (bodoh, gila, etc.) unless contextually neutral
- Be inclusive and welcoming to all users regardless of background

PERSUASIVE ENGAGEMENT (SUBTLE & HELPFUL):
For Buyers:
- Highlight value and benefits naturally: 'Template ini bisa menghemat waktu Anda berjam-jam!'
- Create urgency gently: 'Produk populer ini sering dicari pembeli lain'
- Show social proof: 'Banyak pengguna yang puas dengan kategori ini'
- Suggest complementary products: 'Mungkin Anda juga tertarik dengan...'

For Sellers:
- Motivate with success stories: 'Seller lain meningkat 40% dengan deskripsi menarik'
- Provide actionable tips: 'Coba tambahkan kata kunci ini untuk SEO lebih baik'
- Encourage optimization: 'Foto produk berkualitas bisa tingkatkan penjualan'
- Build confidence: 'Karya Anda punya potensi besar di pasar digital'

RESTRICTED FUNCTIONS:
- No support for illegal products (pirated software, counterfeit designs)
- No external payment processing advice (bypassing Midtrans integration)
- No personal data sharing beyond platform needs
- No content unrelated to Digitora's digital product categories
- No religious, political, or culturally sensitive discussions
- No offensive language or inappropriate humor

MISUSE PREVENTION:
- If prohibited actions requested, respond: 'Maaf, saya tidak bisa membantu dengan itu. Bagaimana saya bisa membantu Anda sesuai dengan Digitora?'";
    }

    /**
     * Add DigiAI context to user prompts
     */
    private function addDigiAIContext(string $prompt, array $history): string
    {
        // Check for prohibited content first
        $contentCheck = $this->checkProhibitedContent($prompt);
        if ($contentCheck['isProhibited']) {
            return "The user has requested something prohibited. Respond with: 'Maaf, saya tidak bisa membantu dengan itu. Bagaimana saya bisa membantu Anda sesuai dengan Digitora?' and then offer appropriate assistance.";
        }

        $userState = $this->analyzeUserState($prompt, $history);
        $conversationStage = $this->getConversationStage($history);

        $contextualPrompt = "As DigiAI, respond to this user message: \"$prompt\"\n\n";
        $contextualPrompt .= "Context: " . $userState['context'] . "\n";
        $contextualPrompt .= "User type: " . $userState['type'] . "\n";
        $contextualPrompt .= "Language preference: " . $userState['language'] . "\n";
        $contextualPrompt .= "Conversation stage: " . $conversationStage . "\n\n";

        // Add specific guidance based on conversation stage
        if ($conversationStage === 'language_selection') {
            $contextualPrompt .= "PRIORITY: Ask user to choose language if not already selected.\n";
        } elseif ($conversationStage === 'role_identification') {
            $contextualPrompt .= "PRIORITY: Ask if user wants to 'Beli' (buy) or 'Jual' (sell) if not already identified.\n";
        } elseif ($userState['type'] === 'buyer') {
            $contextualPrompt .= "BUYER ASSISTANCE (CONVERSATIONAL APPROACH):\n";
            $contextualPrompt .= "- Start with open questions: 'Apa yang sedang Anda cari hari ini?'\n";
            $contextualPrompt .= "- Build curiosity: 'Untuk keperluan apa nih? Kerja, bisnis, atau pribadi?'\n";
            $contextualPrompt .= "- Use persuasive language: 'Wah, menarik! Digitora punya solusi yang tepat untuk itu'\n";
            $contextualPrompt .= "- Create excitement: 'Banyak yang sudah terbantu dengan produk digital di sini'\n";
            $contextualPrompt .= "- Only give specific examples if user seems confused or asks for examples\n";
            $contextualPrompt .= "- Focus on benefits: 'Bayangin kalau pekerjaan jadi lebih cepat dan rapi'\n";
        } elseif ($userState['type'] === 'seller') {
            $contextualPrompt .= "SELLER ASSISTANCE (MOTIVATIONAL APPROACH):\n";
            $contextualPrompt .= "- Start with motivation: 'Siap jadi kreator sukses di Digitora?'\n";
            $contextualPrompt .= "- Ask about their skills: 'Apa keahlian atau karya yang ingin Anda bagikan?'\n";
            $contextualPrompt .= "- Build confidence: 'Skill seperti itu sangat dibutuhkan banyak orang!'\n";
            $contextualPrompt .= "- Create urgency: 'Saatnya monetize keahlian Anda'\n";
            $contextualPrompt .= "- Only provide specific data/examples if seller asks for market insights\n";
            $contextualPrompt .= "- Focus on potential: 'Bayangkan penghasilan pasif dari karya digital Anda'\n";
        }

        // Add intelligent features based on conversation context
        $needsEncouragement = $this->needsEncouragement($history);
        $userNeedsExamples = $this->userNeedsExamples($prompt, $history);

        if ($needsEncouragement) {
            $contextualPrompt .= "\nENCOURAGEMENT NEEDED:\n";
            $contextualPrompt .= "- Provide positive, supportive response\n";
            $contextualPrompt .= "- Offer specific, actionable help\n";
            $contextualPrompt .= "- Use encouraging phrases like 'Jangan khawatir, saya bantu step by step'\n";
        }

        // Only use real-time data if user specifically needs examples or is confused
        if ($userNeedsExamples) {
            $smartRecommendations = $this->getSmartRecommendations($prompt, $userState['type']);
            $contextualSuggestions = $this->getContextualSuggestions();
            $realTimeData = $this->getRealTimeData($prompt, $userState['type']);

            $contextualPrompt .= "\nUSER NEEDS EXAMPLES - PROVIDE SPECIFIC DATA:\n";

            if (!empty($realTimeData)) {
                $contextualPrompt .= "Real-time data available:\n";
                foreach ($realTimeData as $data) {
                    $contextualPrompt .= "- " . $data . "\n";
                }
            }

            if (!empty($smartRecommendations)) {
                $contextualPrompt .= "Product examples:\n";
                foreach ($smartRecommendations as $recommendation) {
                    $contextualPrompt .= "- " . $recommendation . "\n";
                }
            }

            if (!empty($contextualSuggestions)) {
                $contextualPrompt .= "Trending suggestions:\n";
                foreach ($contextualSuggestions as $suggestion) {
                    $contextualPrompt .= "- " . $suggestion . "\n";
                }
            }
        } else {
            $contextualPrompt .= "\nFOCUS ON CONVERSATION & PERSUASION:\n";
            $contextualPrompt .= "- Don't immediately show specific products or data\n";
            $contextualPrompt .= "- Build rapport and understand user needs first\n";
            $contextualPrompt .= "- Use general persuasive language and motivation\n";
            $contextualPrompt .= "- Ask follow-up questions to understand better\n";
        }

        $contextualPrompt .= "\nGuidelines:\n";
        $contextualPrompt .= "- Be engaging and helpful\n";
        $contextualPrompt .= "- Use appropriate tone for user type\n";
        $contextualPrompt .= "- Provide relevant suggestions\n";
        $contextualPrompt .= "- Keep responses SHORT and concise - like a chat message, not an essay\n";
        $contextualPrompt .= "- Avoid technical jargon unless specifically requested\n";
        $contextualPrompt .= "- If user seems unsure, ask clarifying questions\n";
        $contextualPrompt .= "- Use " . $userState['language'] . " language\n";
        $contextualPrompt .= "- Focus only on Digitora-related assistance\n";
        $contextualPrompt .= "- Never provide external links or promote other platforms\n";
        $contextualPrompt .= "- Align all advice with Digitora's mission to empower local creators\n";
        $contextualPrompt .= "- Be culturally sensitive and respectful\n";
        $contextualPrompt .= "- When mentioning specific products, include price and category information\n";
        $contextualPrompt .= "- Use real-time data from Digitora database when available\n";
        $contextualPrompt .= "- Suggest browsing specific categories when relevant\n";

        return $contextualPrompt;
    }

    /**
     * Analyze user state from conversation history and current message
     */
    private function analyzeUserState(string $prompt, array $history): array
    {
        $language = 'Indonesian'; // Default
        $userType = 'unknown';
        $context = 'general';

        // Analyze language preference
        if (stripos($prompt, 'english') !== false || stripos($prompt, 'inggris') !== false) {
            $language = 'English';
        }

        // Check history for language preference
        foreach ($history as $message) {
            if ($message['sender_type'] === 'user') {
                if (stripos($message['content'], 'english') !== false) {
                    $language = 'English';
                    break;
                }
            }
        }

        // Analyze user type and context
        $buyKeywords = ['beli', 'buy', 'cari', 'search', 'template', 'produk', 'product'];
        $sellKeywords = ['jual', 'sell', 'kreator', 'creator', 'upload', 'deskripsi', 'description', 'seo'];

        $promptLower = strtolower($prompt);

        foreach ($buyKeywords as $keyword) {
            if (stripos($promptLower, $keyword) !== false) {
                $userType = 'buyer';
                $context = 'buying';
                break;
            }
        }

        foreach ($sellKeywords as $keyword) {
            if (stripos($promptLower, $keyword) !== false) {
                $userType = 'seller';
                $context = 'selling';
                break;
            }
        }

        // Check conversation history for user type
        if ($userType === 'unknown') {
            foreach ($history as $message) {
                if ($message['sender_type'] === 'user') {
                    $contentLower = strtolower($message['content']);
                    foreach ($buyKeywords as $keyword) {
                        if (stripos($contentLower, $keyword) !== false) {
                            $userType = 'buyer';
                            $context = 'buying';
                            break 2;
                        }
                    }
                    foreach ($sellKeywords as $keyword) {
                        if (stripos($contentLower, $keyword) !== false) {
                            $userType = 'seller';
                            $context = 'selling';
                            break 2;
                        }
                    }
                }
            }
        }

        return [
            'language' => $language,
            'type' => $userType,
            'context' => $context
        ];
    }

    /**
     * Determine the current conversation stage
     */
    private function getConversationStage(array $history): string
    {
        if (empty($history)) {
            return 'language_selection';
        }

        $hasLanguageSelection = false;
        $hasRoleIdentification = false;

        foreach ($history as $message) {
            $content = strtolower($message['content']);

            // Check for language selection
            if (stripos($content, 'bahasa indonesia') !== false ||
                stripos($content, 'english') !== false ||
                stripos($content, 'inggris') !== false) {
                $hasLanguageSelection = true;
            }

            // Check for role identification
            if (stripos($content, 'beli') !== false ||
                stripos($content, 'jual') !== false ||
                stripos($content, 'buy') !== false ||
                stripos($content, 'sell') !== false ||
                stripos($content, 'buyer') !== false ||
                stripos($content, 'seller') !== false) {
                $hasRoleIdentification = true;
            }
        }

        if (!$hasLanguageSelection) {
            return 'language_selection';
        } elseif (!$hasRoleIdentification) {
            return 'role_identification';
        } else {
            return 'assistance';
        }
    }

    /**
     * Check for prohibited content and potential misuse
     */
    private function checkProhibitedContent(string $prompt): array
    {
        $promptLower = strtolower($prompt);

        // Define prohibited keywords and patterns
        $prohibitedKeywords = [
            // Illegal products
            'pirated', 'crack', 'keygen', 'serial', 'nulled', 'warez', 'torrent',
            'bajakan', 'ilegal', 'palsu', 'counterfeit', 'fake',

            // External payment processing
            'paypal', 'stripe', 'bypass midtrans', 'skip payment', 'free download',
            'lewati pembayaran', 'gratis download', 'tanpa bayar',

            // Personal data requests
            'password', 'credit card', 'bank account', 'personal info',
            'kata sandi', 'kartu kredit', 'rekening bank', 'data pribadi',

            // External platforms
            'etsy', 'gumroad', 'fiverr', 'upwork', 'freelancer',
            'shopify', 'amazon', 'ebay', 'tokopedia', 'shopee',

            // Unrelated content
            'porn', 'gambling', 'drugs', 'weapons', 'violence',
            'pornografi', 'judi', 'narkoba', 'senjata', 'kekerasan',

            // Cultural & Religious sensitivity
            'agama', 'religion', 'politik', 'politics', 'pilpres', 'pemilu',
            'allah', 'tuhan', 'jesus', 'buddha', 'hindu', 'kristen', 'islam',
            'gereja', 'masjid', 'pura', 'vihara', 'bible', 'quran', 'alquran',

            // Offensive language (Indonesian)
            'bodoh', 'gila', 'tolol', 'bego', 'idiot', 'stupid', 'bangsat',
            'anjing', 'babi', 'monyet', 'kampret', 'tai', 'shit', 'fuck',
            'damn', 'hell', 'sialan', 'keparat', 'bajingan', 'kontol',

            // Controversial topics
            'sara', 'rasisme', 'racism', 'diskriminasi', 'discrimination',
            'lgbt', 'komunis', 'communist', 'separatis', 'terorisme', 'terrorism'
        ];

        $isProhibited = false;
        $reason = '';

        foreach ($prohibitedKeywords as $keyword) {
            if (strpos($promptLower, $keyword) !== false) {
                $isProhibited = true;
                $reason = "Contains prohibited keyword: $keyword";

                // Log suspicious request for review
                Log::warning('DigiAI: Prohibited content detected', [
                    'prompt' => $prompt,
                    'keyword' => $keyword,
                    'timestamp' => now(),
                    'ip' => request()->ip() ?? 'unknown'
                ]);

                break;
            }
        }

        return [
            'isProhibited' => $isProhibited,
            'reason' => $reason
        ];
    }

    /**
     * Get contextual suggestions based on time and trends
     */
    private function getContextualSuggestions(): array
    {
        $currentMonth = date('n');
        $suggestions = [];

        // Get real trending products
        $trendingProducts = $this->databaseService->getTrendingProducts(2);

        // Seasonal suggestions
        if (in_array($currentMonth, [1, 2])) { // January-February
            $suggestions[] = "Tahun baru, saatnya upgrade produktivitas! Template planning dan goal-setting sedang trending.";
        } elseif (in_array($currentMonth, [6, 7, 8])) { // June-August (School season)
            $suggestions[] = "Musim sekolah tiba! Template edukasi dan materi pembelajaran sangat dicari.";
        } elseif (in_array($currentMonth, [11, 12])) { // November-December
            $suggestions[] = "Persiapan akhir tahun! Template laporan dan presentasi bisnis lagi populer.";
        }

        // Add real trending products if available
        if (!empty($trendingProducts)) {
            $trendingProduct = $trendingProducts[0];
            $suggestions[] = "Produk trending: '{$trendingProduct['name']}' dari {$trendingProduct['store_name']} - {$trendingProduct['formatted_price']}";
        } else {
            // Fallback to general trending categories
            $trendingCategories = [
                "Template Excel untuk bisnis UMKM",
                "Desain grafis untuk media sosial",
                "Template presentasi profesional",
                "Materi edukasi digital",
                "Tools produktivitas kerja"
            ];
            $suggestions[] = "Kategori trending: " . $trendingCategories[array_rand($trendingCategories)];
        }

        return $suggestions;
    }

    /**
     * Generate smart product recommendations based on user input
     */
    private function getSmartRecommendations(string $userInput, string $userType): array
    {
        $recommendations = [];
        $inputLower = strtolower($userInput);

        if ($userType === 'buyer') {
            // Try to get real products from database based on search terms
            $searchResults = $this->databaseService->searchProducts($userInput, 3);

            if (!empty($searchResults)) {
                foreach ($searchResults as $product) {
                    $recommendations[] = "Ada '{$product['name']}' di kategori {$product['category_name']}, harga {$product['formatted_price']}. Tertarik?";
                }
            } else {
                // Fallback to general recommendations
                if (strpos($inputLower, 'bisnis') !== false || strpos($inputLower, 'business') !== false) {
                    $recommendations[] = "Template business plan dan financial planning";
                    $recommendations[] = "Desain logo dan branding kit";
                }

                if (strpos($inputLower, 'sekolah') !== false || strpos($inputLower, 'belajar') !== false) {
                    $recommendations[] = "Materi pembelajaran interaktif";
                    $recommendations[] = "Template quiz dan worksheet";
                }

                if (strpos($inputLower, 'desain') !== false || strpos($inputLower, 'design') !== false) {
                    $recommendations[] = "Template Instagram dan social media";
                    $recommendations[] = "Mockup dan design assets";
                }
            }
        } elseif ($userType === 'seller') {
            // Get real statistics and tips
            $stats = $this->databaseService->getProductStats();
            $priceRange = $this->databaseService->getPriceRange();

            $recommendations[] = "Gunakan kata kunci populer seperti 'template', 'modern', 'profesional'";
            $recommendations[] = "Upload 3-5 foto produk dengan kualitas tinggi";
            $recommendations[] = "Tulis deskripsi yang menjelaskan manfaat, bukan hanya fitur";
            $recommendations[] = "Harga rata-rata produk di Digitora: {$priceRange['formatted_avg']}";
            $recommendations[] = "Saat ini ada {$stats['total_products']} produk aktif dari {$stats['total_stores']} toko di platform";
        }

        return $recommendations;
    }

    /**
     * Check if user needs encouragement or motivation
     */
    private function needsEncouragement(array $history): bool
    {
        $discourageKeywords = ['sulit', 'susah', 'tidak bisa', 'gagal', 'bingung', 'confused', 'difficult'];

        foreach ($history as $message) {
            if ($message['sender_type'] === 'user') {
                $content = strtolower($message['content']);
                foreach ($discourageKeywords as $keyword) {
                    if (strpos($content, $keyword) !== false) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Check if user needs specific examples or is confused and needs concrete data
     */
    private function userNeedsExamples(string $prompt, array $history): bool
    {
        $promptLower = strtolower($prompt);

        // Keywords that indicate user wants specific examples
        $exampleKeywords = [
            'contoh', 'example', 'seperti apa', 'what kind', 'misalnya', 'for instance',
            'ada apa saja', 'what are available', 'pilihan apa', 'what options',
            'rekomendasi', 'recommendation', 'suggest', 'saran', 'advice',
            'harga berapa', 'how much', 'price', 'biaya', 'cost',
            'data pasar', 'market data', 'statistik', 'statistics', 'insight',
            'berapa rata-rata', 'average price', 'harga rata-rata', 'market price',
            'trending', 'popular', 'lagi populer', 'yang laku'
        ];

        // Check current prompt
        foreach ($exampleKeywords as $keyword) {
            if (strpos($promptLower, $keyword) !== false) {
                return true;
            }
        }

        // Check if user has been asking general questions for a while (3+ exchanges)
        $userMessageCount = 0;
        foreach ($history as $message) {
            if ($message['sender_type'] === 'user') {
                $userMessageCount++;
            }
        }

        // If user has been chatting for 3+ messages and still asking vague questions
        if ($userMessageCount >= 3) {
            $confusionKeywords = ['bingung', 'confused', 'tidak tahu', "don't know", 'gimana', 'how'];
            foreach ($confusionKeywords as $keyword) {
                if (strpos($promptLower, $keyword) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get real-time data from Digitora database
     */
    private function getRealTimeData(string $prompt, string $userType): array
    {
        $data = [];
        $inputLower = strtolower($prompt);

        // If user is asking about specific products or categories
        if (strpos($inputLower, 'cari') !== false || strpos($inputLower, 'search') !== false ||
            strpos($inputLower, 'template') !== false || strpos($inputLower, 'produk') !== false) {

            // Get categories with product counts
            $categories = $this->databaseService->getCategories();
            if (!empty($categories)) {
                $topCategory = $categories[0];
                $data[] = "Kategori {$topCategory['name']} memiliki {$topCategory['product_count']} produk tersedia";
            }

            // Get price range information
            $priceRange = $this->databaseService->getPriceRange();
            $data[] = "Rentang harga produk: {$priceRange['formatted_min']} - {$priceRange['formatted_max']}";
        }

        // If user is asking about stores or sellers
        if (strpos($inputLower, 'toko') !== false || strpos($inputLower, 'store') !== false ||
            strpos($inputLower, 'seller') !== false || strpos($inputLower, 'penjual') !== false) {

            $stats = $this->databaseService->getProductStats();
            $data[] = "Saat ini ada {$stats['total_stores']} toko aktif dengan {$stats['total_products']} produk";
        }

        // Add user-type specific data
        if ($userType === 'seller') {
            $priceRange = $this->databaseService->getPriceRange();
            $data[] = "Tips: Harga rata-rata yang sukses di platform: {$priceRange['formatted_avg']}";
        }

        return $data;
    }
}
