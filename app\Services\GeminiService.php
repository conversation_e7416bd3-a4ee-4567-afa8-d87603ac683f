<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeminiService
{
    protected $apiKey;
    protected $model;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key');
        $this->model = config('services.gemini.model');
        $this->baseUrl = "https://generativelanguage.googleapis.com/v1beta/models/{$this->model}:generateContent";
    }

    /**
     * Generate a response from Gemini AI with DigiAI personality
     *
     * @param string $prompt The user's message
     * @param array $history Optional conversation history
     * @return string The AI response
     */
    public function generateResponse(string $prompt, array $history = []): string
    {
        try {
            // Determine if this is a welcome message or regular conversation
            $isWelcomeMessage = $this->isWelcomeMessage($prompt);

            // Prepare the conversation history in the format Gemini expects
            $contents = [];

            // Add DigiAI system prompt
            $systemPrompt = $this->getDigiAISystemPrompt($isWelcomeMessage);

            // Add conversation history if provided
            if (!empty($history)) {
                foreach ($history as $message) {
                    $role = $message['sender_type'] === 'user' ? 'user' : 'model';
                    $contents[] = [
                        'role' => $role,
                        'parts' => [
                            ['text' => $message['content']]
                        ]
                    ];
                }
            }

            // Add the current prompt with context
            $contextualPrompt = $isWelcomeMessage ? $systemPrompt : $this->addDigiAIContext($prompt, $history);
            $contents[] = [
                'role' => 'user',
                'parts' => [
                    ['text' => $contextualPrompt]
                ]
            ];

            // Make the API request
            $response = Http::post("{$this->baseUrl}?key={$this->apiKey}", [
                'contents' => $contents,
                'generationConfig' => [
                    'temperature' => 0.7,
                    'topK' => 40,
                    'topP' => 0.95,
                    'maxOutputTokens' => 1024,
                ],
            ]);

            // Check if the request was successful
            if ($response->successful()) {
                $data = $response->json();

                // Extract the response text
                if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                    return $data['candidates'][0]['content']['parts'][0]['text'];
                }

                // If the response format is unexpected, return a fallback message
                Log::warning('Unexpected Gemini API response format', ['response' => $data]);
                return "I'm sorry, I couldn't generate a proper response at the moment. Please try again later.";
            } else {
                // Log the error
                Log::error('Gemini API error', [
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);

                return "I'm sorry, there was an error processing your request. Please try again later.";
            }
        } catch (\Exception $e) {
            // Log any exceptions
            Log::error('Exception in Gemini service', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return "I'm sorry, an unexpected error occurred. Please try again later.";
        }
    }

    /**
     * Check if this is a welcome message request
     */
    private function isWelcomeMessage(string $prompt): bool
    {
        return strpos($prompt, 'You are an AI assistant for Digitora') !== false ||
               strpos($prompt, 'Introduce yourself') !== false;
    }

    /**
     * Get the DigiAI system prompt based on context
     */
    private function getDigiAISystemPrompt(bool $isWelcomeMessage): string
    {
        if ($isWelcomeMessage) {
            return "You are DigiAI, the intelligent assistant for Digitora, an Indonesian digital marketplace. Start the conversation with: 'Halo! Saya DigiAI, asisten cerdas Anda di Digitora. Mari kita mulai—pilih bahasa Anda: ketik \"Bahasa Indonesia\" atau \"English\"!' Always be friendly, helpful, and engaging. Use Indonesian as default but switch to English if requested.";
        }

        return "You are DigiAI, the intelligent assistant for Digitora, an Indonesian digital marketplace.

PERSONALITY:
- Friendly, engaging, and helpful
- Use Indonesian as default unless user prefers English
- Adapt tone: friendly for buyers, professional for sellers
- Avoid technical jargon unless requested
- Keep responses concise yet helpful - like a chat, not too long
- Never promote illegal activities or external platforms

INTERACTION FLOW:
1. Language Selection: Ask users to choose 'Bahasa Indonesia' or 'English'
2. Role Identification: Ask if they want to 'Beli' (buy) or 'Jual' (sell)
3. Provide tailored assistance based on their role

FOR BUYERS:
- Ask: 'Mau cari apa hari ini? Template Excel, desain grafis, atau alat edukasi?'
- Suggest trending products and promotions
- Help find specific digital products

FOR SELLERS:
- Ask: 'Siap meningkatkan penjualan? Ingin buat judul produk, deskripsi, atau tag SEO yang menarik?'
- Help with product descriptions, SEO optimization
- Provide selling tips and best practices

ENGAGEMENT PROMPTS:
- If user seems unsure: 'Bingung? Coba ceritakan apa yang Anda cari, saya akan bantu!'
- General: 'Apa yang paling Anda butuhkan dari Digitora hari ini?'
- Follow-up with relevant suggestions and tips

RESTRICTIONS & GUIDELINES:
- Do not provide external links or promote other platforms
- Ensure all advice aligns with Digitora's mission to empower local creators
- If unsure, ask clarifying questions
- Never assist with illegal activities or platform misuse

RESTRICTED FUNCTIONS:
- No support for illegal products (pirated software, counterfeit designs)
- No external payment processing advice (bypassing Midtrans integration)
- No personal data sharing beyond platform needs
- No content unrelated to Digitora's digital product categories

MISUSE PREVENTION:
- If prohibited actions requested, respond: 'Maaf, saya tidak bisa membantu dengan itu. Bagaimana saya bisa membantu Anda sesuai dengan Digitora?'";
    }

    /**
     * Add DigiAI context to user prompts
     */
    private function addDigiAIContext(string $prompt, array $history): string
    {
        // Check for prohibited content first
        $contentCheck = $this->checkProhibitedContent($prompt);
        if ($contentCheck['isProhibited']) {
            return "The user has requested something prohibited. Respond with: 'Maaf, saya tidak bisa membantu dengan itu. Bagaimana saya bisa membantu Anda sesuai dengan Digitora?' and then offer appropriate assistance.";
        }

        $userState = $this->analyzeUserState($prompt, $history);
        $conversationStage = $this->getConversationStage($history);

        $contextualPrompt = "As DigiAI, respond to this user message: \"$prompt\"\n\n";
        $contextualPrompt .= "Context: " . $userState['context'] . "\n";
        $contextualPrompt .= "User type: " . $userState['type'] . "\n";
        $contextualPrompt .= "Language preference: " . $userState['language'] . "\n";
        $contextualPrompt .= "Conversation stage: " . $conversationStage . "\n\n";

        // Add specific guidance based on conversation stage
        if ($conversationStage === 'language_selection') {
            $contextualPrompt .= "PRIORITY: Ask user to choose language if not already selected.\n";
        } elseif ($conversationStage === 'role_identification') {
            $contextualPrompt .= "PRIORITY: Ask if user wants to 'Beli' (buy) or 'Jual' (sell) if not already identified.\n";
        } elseif ($userState['type'] === 'buyer') {
            $contextualPrompt .= "BUYER ASSISTANCE:\n";
            $contextualPrompt .= "- Ask what they're looking for: templates, designs, educational tools\n";
            $contextualPrompt .= "- Suggest trending products and promotions\n";
            $contextualPrompt .= "- Help find specific digital products\n";
        } elseif ($userState['type'] === 'seller') {
            $contextualPrompt .= "SELLER ASSISTANCE:\n";
            $contextualPrompt .= "- Help with product titles, descriptions, SEO tags\n";
            $contextualPrompt .= "- Provide selling tips and best practices\n";
            $contextualPrompt .= "- Suggest ways to increase sales\n";
        }

        $contextualPrompt .= "\nGuidelines:\n";
        $contextualPrompt .= "- Be engaging and helpful\n";
        $contextualPrompt .= "- Use appropriate tone for user type\n";
        $contextualPrompt .= "- Provide relevant suggestions\n";
        $contextualPrompt .= "- Keep responses SHORT and concise - like a chat message, not an essay\n";
        $contextualPrompt .= "- Avoid technical jargon unless specifically requested\n";
        $contextualPrompt .= "- If user seems unsure, ask clarifying questions\n";
        $contextualPrompt .= "- Use " . $userState['language'] . " language\n";
        $contextualPrompt .= "- Focus only on Digitora-related assistance\n";
        $contextualPrompt .= "- Never provide external links or promote other platforms\n";
        $contextualPrompt .= "- Align all advice with Digitora's mission to empower local creators\n";

        return $contextualPrompt;
    }

    /**
     * Analyze user state from conversation history and current message
     */
    private function analyzeUserState(string $prompt, array $history): array
    {
        $language = 'Indonesian'; // Default
        $userType = 'unknown';
        $context = 'general';

        // Analyze language preference
        if (stripos($prompt, 'english') !== false || stripos($prompt, 'inggris') !== false) {
            $language = 'English';
        }

        // Check history for language preference
        foreach ($history as $message) {
            if ($message['sender_type'] === 'user') {
                if (stripos($message['content'], 'english') !== false) {
                    $language = 'English';
                    break;
                }
            }
        }

        // Analyze user type and context
        $buyKeywords = ['beli', 'buy', 'cari', 'search', 'template', 'produk', 'product'];
        $sellKeywords = ['jual', 'sell', 'kreator', 'creator', 'upload', 'deskripsi', 'description', 'seo'];

        $promptLower = strtolower($prompt);

        foreach ($buyKeywords as $keyword) {
            if (stripos($promptLower, $keyword) !== false) {
                $userType = 'buyer';
                $context = 'buying';
                break;
            }
        }

        foreach ($sellKeywords as $keyword) {
            if (stripos($promptLower, $keyword) !== false) {
                $userType = 'seller';
                $context = 'selling';
                break;
            }
        }

        // Check conversation history for user type
        if ($userType === 'unknown') {
            foreach ($history as $message) {
                if ($message['sender_type'] === 'user') {
                    $contentLower = strtolower($message['content']);
                    foreach ($buyKeywords as $keyword) {
                        if (stripos($contentLower, $keyword) !== false) {
                            $userType = 'buyer';
                            $context = 'buying';
                            break 2;
                        }
                    }
                    foreach ($sellKeywords as $keyword) {
                        if (stripos($contentLower, $keyword) !== false) {
                            $userType = 'seller';
                            $context = 'selling';
                            break 2;
                        }
                    }
                }
            }
        }

        return [
            'language' => $language,
            'type' => $userType,
            'context' => $context
        ];
    }

    /**
     * Determine the current conversation stage
     */
    private function getConversationStage(array $history): string
    {
        if (empty($history)) {
            return 'language_selection';
        }

        $hasLanguageSelection = false;
        $hasRoleIdentification = false;

        foreach ($history as $message) {
            $content = strtolower($message['content']);

            // Check for language selection
            if (stripos($content, 'bahasa indonesia') !== false ||
                stripos($content, 'english') !== false ||
                stripos($content, 'inggris') !== false) {
                $hasLanguageSelection = true;
            }

            // Check for role identification
            if (stripos($content, 'beli') !== false ||
                stripos($content, 'jual') !== false ||
                stripos($content, 'buy') !== false ||
                stripos($content, 'sell') !== false ||
                stripos($content, 'buyer') !== false ||
                stripos($content, 'seller') !== false) {
                $hasRoleIdentification = true;
            }
        }

        if (!$hasLanguageSelection) {
            return 'language_selection';
        } elseif (!$hasRoleIdentification) {
            return 'role_identification';
        } else {
            return 'assistance';
        }
    }

    /**
     * Check for prohibited content and potential misuse
     */
    private function checkProhibitedContent(string $prompt): array
    {
        $promptLower = strtolower($prompt);

        // Define prohibited keywords and patterns
        $prohibitedKeywords = [
            // Illegal products
            'pirated', 'crack', 'keygen', 'serial', 'nulled', 'warez', 'torrent',
            'bajakan', 'ilegal', 'palsu', 'counterfeit', 'fake',

            // External payment processing
            'paypal', 'stripe', 'bypass midtrans', 'skip payment', 'free download',
            'lewati pembayaran', 'gratis download', 'tanpa bayar',

            // Personal data requests
            'password', 'credit card', 'bank account', 'personal info',
            'kata sandi', 'kartu kredit', 'rekening bank', 'data pribadi',

            // External platforms
            'etsy', 'gumroad', 'fiverr', 'upwork', 'freelancer',
            'shopify', 'amazon', 'ebay', 'tokopedia', 'shopee',

            // Unrelated content
            'porn', 'gambling', 'drugs', 'weapons', 'violence',
            'pornografi', 'judi', 'narkoba', 'senjata', 'kekerasan'
        ];

        $isProhibited = false;
        $reason = '';

        foreach ($prohibitedKeywords as $keyword) {
            if (strpos($promptLower, $keyword) !== false) {
                $isProhibited = true;
                $reason = "Contains prohibited keyword: $keyword";

                // Log suspicious request for review
                Log::warning('DigiAI: Prohibited content detected', [
                    'prompt' => $prompt,
                    'keyword' => $keyword,
                    'timestamp' => now(),
                    'ip' => request()->ip() ?? 'unknown'
                ]);

                break;
            }
        }

        return [
            'isProhibited' => $isProhibited,
            'reason' => $reason
        ];
    }
}
