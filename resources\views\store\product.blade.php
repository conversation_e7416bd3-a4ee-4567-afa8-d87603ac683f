@extends('store.layout')

@section('head')
    <!-- Page Context Meta Tags for DigiAI -->
    <meta name="product-id" content="{{ $product->id }}">
    <meta name="store-slug" content="{{ $seller->store_name_slug }}">
    <meta name="product-slug" content="{{ $product->slug }}">
@endsection

@section('content')
    <div class="py-10 bg-gray-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumbs -->
            <nav class="flex mb-6" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('store.show', $seller->store_slug) }}"
                            class="text-gray-500 hover:text-indigo-600 text-sm font-medium transition duration-150 ease-in-out">
                            Home
                        </a>
                    </li>

                    @if (isset($hasNewCategories) && $hasNewCategories)
                        @if ($product->productCategory)
                            <li>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd"
                                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    <a href="{{ route('store.category', [$seller->store_slug, $product->productCategory->slug]) }}"
                                        class="text-gray-500 hover:text-indigo-600 ml-1 text-sm font-medium transition duration-150 ease-in-out">
                                        {{ $product->productCategory->name }}
                                    </a>
                                </div>
                            </li>
                        @endif

                        @if ($product->productSubcategory)
                            <li>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd"
                                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    <a href="{{ route('store.category', [$seller->store_slug, $product->productSubcategory->slug]) }}"
                                        class="text-gray-500 hover:text-indigo-600 ml-1 text-sm font-medium transition duration-150 ease-in-out">
                                        {{ $product->productSubcategory->name }}
                                    </a>
                                </div>
                            </li>
                        @endif

                        @if ($product->productDetailedCategory)
                            <li>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd"
                                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    <a href="{{ route('store.category', [$seller->store_slug, $product->productDetailedCategory->slug]) }}"
                                        class="text-gray-500 hover:text-indigo-600 ml-1 text-sm font-medium transition duration-150 ease-in-out">
                                        {{ $product->productDetailedCategory->name }}
                                    </a>
                                </div>
                            </li>
                        @endif
                    @else
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd"
                                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <a href="{{ route('store.category', [$seller->store_slug, $product->category]) }}"
                                    class="text-gray-500 hover:text-indigo-600 ml-1 text-sm font-medium transition duration-150 ease-in-out">
                                    {{ ucfirst($product->category) }}
                                </a>
                            </div>
                        </li>
                    @endif

                    <li>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                    clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600 ml-1 text-sm font-medium">{{ $product->name }}</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Back to Store Link -->
            <div class="mb-6">
                <a href="{{ route('store.show', $seller->store_slug) }}"
                    class="inline-flex items-center text-indigo-600 hover:text-indigo-800 transition duration-150 ease-in-out">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Back to {{ $seller->sellerApplication->store_name }}
                </a>
            </div>

            <!-- Product -->
            <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
                    <!-- Product Images -->
                    <div>
                        @php
                            // Get product images ordered by sort_order
                            $productImages = $product->images()->orderBy('sort_order')->get();

                            // Get the primary image or the first image in the collection
                            $primaryImage =
                                $productImages->where('is_primary', true)->first() ?? $productImages->first();

                            // Fallback to the legacy image field if no images in the collection
                            $mainImagePath = $primaryImage
                                ? asset('storage/' . $primaryImage->path)
                                : ($product->image
                                    ? asset('storage/' . $product->image)
                                    : asset('images/placeholder.jpg'));
                        @endphp
                        <div
                            class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden bg-gray-100 mb-4 shadow-sm hover:shadow-md transition duration-300">
                            <img id="main-image" src="{{ $mainImagePath }}" alt="{{ $product->name }}"
                                class="w-full h-full object-center object-cover">
                            @if ($product->discount_price)
                                <div
                                    class="absolute top-4 right-4 bg-red-500 text-white text-sm font-bold px-3 py-1 rounded-full shadow-md">
                                    {{ round((($product->price - $product->discount_price) / $product->price) * 100) }}%
                                    OFF
                                </div>
                            @endif
                        </div>
                        <div class="grid grid-cols-4 gap-4" id="thumbnails-container">
                            @if ($productImages->count() > 0)
                                @foreach ($productImages as $index => $image)
                                    <div class="aspect-w-1 aspect-h-1 rounded-lg overflow-hidden bg-gray-100 cursor-pointer {{ $image->is_primary || ($index == 0 && !$productImages->where('is_primary', true)->count()) ? 'border-2 border-indigo-500' : 'hover:opacity-80' }} transition duration-300 thumbnail-container"
                                        data-index="{{ $index }}">
                                        <img src="{{ asset('storage/' . $image->path) }}" alt="{{ $product->name }}"
                                            class="w-full h-full object-center object-cover thumbnail"
                                            data-src="{{ asset('storage/' . $image->path) }}">
                                    </div>
                                @endforeach

                                @for ($i = $productImages->count(); $i < 4; $i++)
                                    <div
                                        class="aspect-w-1 aspect-h-1 rounded-lg overflow-hidden bg-gray-100 cursor-pointer hover:opacity-80 transition duration-300">
                                        <div class="w-full h-full flex items-center justify-center text-gray-400">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                    </div>
                                @endfor
                            @else
                                <div
                                    class="aspect-w-1 aspect-h-1 rounded-lg overflow-hidden bg-gray-100 cursor-pointer border-2 border-indigo-500">
                                    <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}"
                                        alt="{{ $product->name }}"
                                        class="w-full h-full object-center object-cover thumbnail"
                                        data-src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}">
                                </div>
                                @for ($i = 1; $i <= 3; $i++)
                                    <div
                                        class="aspect-w-1 aspect-h-1 rounded-lg overflow-hidden bg-gray-100 cursor-pointer hover:opacity-80 transition duration-300">
                                        <div class="w-full h-full flex items-center justify-center text-gray-400">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                    </div>
                                @endfor
                            @endif
                        </div>
                    </div>

                    <!-- Product Info -->
                    <div>
                        <div class="flex items-center justify-between">
                            <h1 class="text-2xl font-bold text-gray-900 sm:text-3xl">{{ $product->name }}</h1>
                            <span class="px-3 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">
                                @if (isset($categoryName))
                                    {{ $categoryName }}
                                @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productDetailedCategory)
                                    {{ $product->productDetailedCategory->name }}
                                @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productSubcategory)
                                    {{ $product->productSubcategory->name }}
                                @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productCategory)
                                    {{ $product->productCategory->name }}
                                @else
                                    {{ ucfirst($product->category) }}
                                @endif
                            </span>
                        </div>

                        <div class="mt-3 flex items-center">
                            <div class="flex items-center">
                                @for ($i = 1; $i <= 5; $i++)
                                    @if ($i <= ($product->average_rating ?? 5))
                                        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20" fill="currentColor">
                                            <path
                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @else
                                        <svg class="h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20" fill="currentColor">
                                            <path
                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @endif
                                @endfor
                            </div>
                            <span class="text-sm text-gray-500 ml-2">{{ $product->reviews_count ?? 0 }} reviews</span>
                        </div>

                        <div class="mt-5">
                            @if ($product->discount_price)
                                <div class="flex items-center">
                                    <p class="text-3xl font-bold text-gray-900">
                                        Rp {{ number_format($product->discount_price, 0, ',', '.') }}</p>
                                    <p class="ml-3 text-lg text-gray-500 line-through">Rp
                                        {{ number_format($product->price, 0, ',', '.') }}
                                    </p>
                                </div>
                            @else
                                <p class="text-3xl font-bold text-gray-900">Rp
                                    {{ number_format($product->price, 0, ',', '.') }}</p>
                            @endif
                        </div>

                        <div class="mt-6 border-t border-b py-6">
                            <div id="description">
                                {!! $product->description ?? 'No description available for this product.' !!}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 p-6 bg-gray-50 border-t">
                    <div class="bg-white p-5 rounded-lg shadow-sm">
                        <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-indigo-600" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            Highlights
                        </h3>
                        <ul class="space-y-3">
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-700">Instant digital download</span>
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-700">Compatible with all major software</span>
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-700">Lifetime updates</span>
                            </li>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-700">Premium support</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-white p-5 rounded-lg shadow-sm">
                        <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-indigo-600" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            </svg>
                            What's included
                        </h3>
                        <div class="space-y-3">
                            <div
                                class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-150 ease-in-out">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="ml-3 text-gray-700 font-medium">Main product file</span>
                            </div>
                            <div
                                class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-150 ease-in-out">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="ml-3 text-gray-700 font-medium">Documentation</span>
                            </div>
                            <div
                                class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-150 ease-in-out">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="ml-3 text-gray-700 font-medium">Support guide</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="p-6 bg-white border-t">
                    @if (isset($alreadyPurchased) && $alreadyPurchased)
                        <!-- Already purchased notification -->
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-green-800">You already own this product</h3>
                                    <div class="mt-2 text-sm text-green-700">
                                        <p>You have already purchased this digital product. You can access it from your
                                            purchases page.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <a href="{{ route('user.purchases') }}"
                            class="w-full bg-indigo-600 border border-transparent rounded-lg py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            View Your Purchase
                        </a>
                    @else
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <form action="{{ route('cart.add') }}" method="POST">
                                @csrf
                                <input type="hidden" name="product_id" value="{{ $product->id }}">
                                <input type="hidden" name="quantity" value="1">
                                <button type="submit"
                                    class="w-full bg-indigo-600 border border-transparent rounded-lg py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    Add to Cart
                                </button>
                            </form>
                            <form action="{{ route('product.buy') }}" method="POST" id="buy_form">
                                @csrf
                                <input type="hidden" name="product_id" value="{{ $product->id }}">
                                <button type="submit"
                                    class="w-full bg-gray-800 border border-transparent rounded-lg py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-150 ease-in-out shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7" />
                                    </svg>
                                    Buy Now
                                </button>
                            </form>
                        </div>

                        <div class="mt-6 flex items-center justify-center space-x-6">
                            <div class="flex items-center text-gray-500">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-sm">Secure payment</span>
                            </div>
                            <div class="flex items-center text-gray-500">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-sm">Instant download</span>
                            </div>
                            <div class="flex items-center text-gray-500">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-sm">Money-back guarantee</span>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Related Products -->
        @if ($relatedProducts->count() > 0)
            <div class="mt-12 container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Related Products</h2>
                    <a href="{{ route('store.show', $seller->store_slug) }}"
                        class="text-indigo-600 hover:text-indigo-800 font-medium flex items-center transition duration-150 ease-in-out">
                        View All
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach ($relatedProducts as $relatedProduct)
                        <div
                            class="group relative bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition duration-300 ease-in-out transform hover:-translate-y-1">
                            <div class="aspect-w-4 aspect-h-3 overflow-hidden bg-gray-100">
                                <a href="{{ route('store.product', [$seller->store_slug, $relatedProduct->slug]) }}">
                                    <img src="{{ $relatedProduct->image ? asset('storage/' . $relatedProduct->image) : asset('images/placeholder.jpg') }}"
                                        alt="{{ $relatedProduct->name }}"
                                        class="w-full h-full object-center object-cover group-hover:scale-105 transition duration-500">
                                </a>
                                @if ($relatedProduct->discount_price)
                                    <div
                                        class="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-2.5 py-1.5 rounded-full shadow-sm">
                                        {{ round((($relatedProduct->price - $relatedProduct->discount_price) / $relatedProduct->price) * 100) }}%
                                        OFF
                                    </div>
                                @endif
                            </div>
                            <div class="p-4">
                                <div class="flex items-center justify-between mb-1">
                                    <span
                                        class="text-xs font-medium text-indigo-600 bg-indigo-50 px-2 py-0.5 rounded-full">
                                        @if (isset($hasNewCategories) && $hasNewCategories && $relatedProduct->productDetailedCategory)
                                            {{ $relatedProduct->productDetailedCategory->name }}
                                        @elseif(isset($hasNewCategories) && $hasNewCategories && $relatedProduct->productSubcategory)
                                            {{ $relatedProduct->productSubcategory->name }}
                                        @elseif(isset($hasNewCategories) && $hasNewCategories && $relatedProduct->productCategory)
                                            {{ $relatedProduct->productCategory->name }}
                                        @else
                                            {{ ucfirst($relatedProduct->category) }}
                                        @endif
                                    </span>
                                    <div class="flex items-center">
                                        <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20" fill="currentColor">
                                            <path
                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                        <span
                                            class="text-xs text-gray-500 ml-1">{{ $relatedProduct->average_rating ?? '5.0' }}</span>
                                    </div>
                                </div>
                                <h3 class="text-sm font-medium text-gray-900 mt-2 line-clamp-2 h-10">
                                    <a href="{{ route('store.product', [$seller->store_slug, $relatedProduct->slug]) }}">
                                        {{ $relatedProduct->name }}
                                    </a>
                                </h3>
                                <div class="mt-2">
                                    @if ($relatedProduct->discount_price)
                                        <div class="flex items-center">
                                            <p class="text-sm font-bold text-gray-900">
                                                Rp {{ number_format($relatedProduct->discount_price, 0, ',', '.') }}</p>
                                            <p class="ml-2 text-xs text-gray-500 line-through">
                                                Rp {{ number_format($relatedProduct->price, 0, ',', '.') }}</p>
                                        </div>
                                    @else
                                        <p class="text-sm font-bold text-gray-900">
                                            Rp {{ number_format($relatedProduct->price, 0, ',', '.') }}</p>
                                    @endif
                                </div>
                                <div class="mt-3">
                                    @if (isset($purchasedProductIds) && in_array($relatedProduct->id, $purchasedProductIds))
                                        <a href="{{ route('user.purchases') }}"
                                            class="w-full bg-green-100 hover:bg-green-200 text-green-800 font-medium py-2 px-4 rounded-lg text-xs flex items-center justify-center transition duration-150 ease-in-out">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M5 13l4 4L19 7" />
                                            </svg>
                                            View Purchase
                                        </a>
                                    @else
                                        <form action="{{ route('cart.add') }}" method="POST"
                                            class="related-product-form">
                                            @csrf
                                            <input type="hidden" name="product_id" value="{{ $relatedProduct->id }}">
                                            <input type="hidden" name="quantity" value="1">
                                            <button type="submit"
                                                class="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg text-xs flex items-center justify-center transition duration-150 ease-in-out">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1"
                                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                                </svg>
                                                Add to Cart
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
    </div>
    <input type="hidden" id="csrf_token" value="{{ csrf_token() }}">

    <script
        src="{{ !config('services.midtrans.isProduction') ? 'https://app.sandbox.midtrans.com/snap/snap.js' : 'https://app.midtrans.com/snap/snap.js' }}"
        data-client-key="{{ config('services.midtrans.clientKey') }}"></script>

    <script src="{{ asset(js_path() . '/store-product.js') }}"></script>

    {{-- load tinymce --}}
    <script src="{{ asset('tinymce/tinymce.min.js') }}"></script>

    <script>
        tinymce.init({
            selector: "#description",
            menubar: false,
            statusbar: false,
            toolbar: false,
            readonly: true,
            toolbar_mode: "floating",
            tinycomments_mode: "embedded",
            tinycomments_author: "Author name"
        });
    </script>
@endsection
