<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>DigiAI Context Test - Digitora</title>
    
    <!-- Page Context Meta Tags for DigiAI -->
    <meta name="product-id" content="{{ $product->id }}">
    <meta name="store-slug" content="{{ $seller->store_name_slug }}">
    <meta name="seller-id" content="{{ $seller->id }}">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="{{ asset('css/ai-chat.css') }}" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">DigiAI Page Context Test</h1>
            
            <!-- Product Context Section -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">🛍️ Product Context Test</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-700 mb-2">Current Product:</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p><strong>Name:</strong> {{ $product->name }}</p>
                            <p><strong>Price:</strong> Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                            @if($product->discount_price)
                                <p><strong>Discount Price:</strong> Rp {{ number_format($product->discount_price, 0, ',', '.') }}</p>
                            @endif
                            <p><strong>Category:</strong> {{ $product->category }}</p>
                            <p><strong>Store:</strong> {{ $seller->store_name }}</p>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-700 mb-2">Test Messages:</h3>
                        <div class="space-y-2">
                            <button onclick="testMessage('Ceritakan tentang produk ini')" 
                                    class="w-full text-left bg-blue-50 hover:bg-blue-100 p-3 rounded-lg border border-blue-200 transition-colors">
                                "Ceritakan tentang produk ini"
                            </button>
                            <button onclick="testMessage('Berapa harga produk ini?')" 
                                    class="w-full text-left bg-blue-50 hover:bg-blue-100 p-3 rounded-lg border border-blue-200 transition-colors">
                                "Berapa harga produk ini?"
                            </button>
                            <button onclick="testMessage('Apakah ada diskon?')" 
                                    class="w-full text-left bg-blue-50 hover:bg-blue-100 p-3 rounded-lg border border-blue-200 transition-colors">
                                "Apakah ada diskon?"
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Seller Context Section -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">👨‍💼 Seller Context Test</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-700 mb-2">Current Seller:</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p><strong>Store Name:</strong> {{ $seller->store_name }}</p>
                            <p><strong>Store Slug:</strong> {{ $seller->store_name_slug }}</p>
                            <p><strong>Status:</strong> {{ $seller->status }}</p>
                            <p><strong>Description:</strong> {{ $seller->store_description ?? 'No description' }}</p>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-700 mb-2">Test Messages:</h3>
                        <div class="space-y-2">
                            <button onclick="testMessage('Bagaimana cara meningkatkan penjualan saya?')" 
                                    class="w-full text-left bg-green-50 hover:bg-green-100 p-3 rounded-lg border border-green-200 transition-colors">
                                "Bagaimana cara meningkatkan penjualan saya?"
                            </button>
                            <button onclick="testMessage('Berapa total produk saya?')" 
                                    class="w-full text-left bg-green-50 hover:bg-green-100 p-3 rounded-lg border border-green-200 transition-colors">
                                "Berapa total produk saya?"
                            </button>
                            <button onclick="testMessage('Apa saran untuk toko saya?')" 
                                    class="w-full text-left bg-green-50 hover:bg-green-100 p-3 rounded-lg border border-green-200 transition-colors">
                                "Apa saran untuk toko saya?"
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Page Context Info -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">🔍 Page Context Detection</h2>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p><strong>Current URL:</strong> <span id="current-url"></span></p>
                    <p><strong>Detected Page Type:</strong> <span id="page-type"></span></p>
                    <p><strong>Context Data:</strong></p>
                    <pre id="context-data" class="bg-gray-100 p-2 rounded mt-2 text-sm overflow-auto"></pre>
                </div>
            </div>
            
            <!-- Instructions -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-3">📋 How to Test</h2>
                <ol class="list-decimal list-inside space-y-2 text-blue-700">
                    <li>Open the DigiAI chat (bottom-right corner)</li>
                    <li>Click any test message button above</li>
                    <li>Watch how DigiAI responds with specific product/seller information</li>
                    <li>Try your own messages to see context-aware responses</li>
                    <li>Check the "Page Context Detection" section to see what data is being sent</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- AI Chat Component -->
    @include('components.ai-chat')

    <!-- AI Chat JS -->
    <script src="{{ asset(js_path() . '/ai-chat.js') }}" defer></script>
    
    <script>
        // Function to test messages
        function testMessage(message) {
            // Open chat if not already open
            const chatContainer = document.getElementById('ai-chat-container');
            if (chatContainer && !chatContainer.classList.contains('ai-chat-open')) {
                document.getElementById('ai-chat-toggle').click();
            }
            
            // Wait a bit for chat to open, then send message
            setTimeout(() => {
                const chatInput = document.getElementById('ai-chat-input');
                if (chatInput) {
                    chatInput.value = message;
                    // Trigger send message
                    const sendButton = document.querySelector('#ai-chat-container .ai-chat-send-button');
                    if (sendButton) {
                        sendButton.click();
                    }
                }
            }, 500);
        }
        
        // Update page context info
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('current-url').textContent = window.location.href;
            
            // Get page context (same function from ai-chat.js)
            const context = {
                url: window.location.href,
                pathname: window.location.pathname,
                page_type: 'unknown'
            };

            const path = window.location.pathname;
            
            if (path.includes('/test-digiai-context')) {
                context.page_type = 'test_page';
                const productIdMeta = document.querySelector('meta[name="product-id"]');
                const sellerIdMeta = document.querySelector('meta[name="seller-id"]');
                const storeSlugMeta = document.querySelector('meta[name="store-slug"]');
                
                if (productIdMeta) context.product_id = productIdMeta.content;
                if (sellerIdMeta) context.seller_id = sellerIdMeta.content;
                if (storeSlugMeta) context.store_slug = storeSlugMeta.content;
            }
            
            document.getElementById('page-type').textContent = context.page_type;
            document.getElementById('context-data').textContent = JSON.stringify(context, null, 2);
        });
    </script>
</body>
</html>
