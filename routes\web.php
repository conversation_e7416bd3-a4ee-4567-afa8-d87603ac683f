<?php

use App\Http\Controllers\AiChatController;
use App\Http\Controllers\Auth\SocialiteController;
use App\Models\SellerApplication;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SellerApplicationController;
use App\Http\Controllers\Seller\DashboardController;
use App\Http\Controllers\Seller\ProductController;
use App\Http\Controllers\Seller\OrderController;
use App\Http\Controllers\Seller\AnalyticsController;
use App\Http\Controllers\Seller\DocumentationController;
use App\Http\Controllers\Seller\HelpCenterController;
use App\Http\Controllers\Seller\PaymentController;
use App\Http\Controllers\Seller\SettingsController;
use App\Http\Controllers\PagesController;

Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

Route::get('/auth/google', [SocialiteController::class, 'redirectToGoogle'])->name('auth.google');
Route::get('/auth/google/callback', [SocialiteController::class, 'handleGoogleCallback']);

Route::middleware(['auth'])->group(function () {
    // Route::get('/dashboard', function () {
    //     return view('dashboard');
    // })->name('dashboard');

    // Seller Application Routes
    Route::get('/seller/apply', [SellerApplicationController::class, 'create'])->name('seller.apply');
    Route::post('/seller/apply', [SellerApplicationController::class, 'store'])->name('seller.store');
    Route::get('/seller/success', [SellerApplicationController::class, 'success'])->name('seller.success');
    Route::get('/seller/file/{applicationId}/{type}', [SellerApplicationController::class, 'serveFile'])->name('seller.file');
    Route::get('/seller/reapply', [SellerApplicationController::class, 'reapply'])->name('seller.reapply');
    Route::get('/seller/pending', function () {
        $user = auth()->user();
        $application = \App\Models\SellerApplication::where('user_id', $user->id)->first();

        // If no application exists, redirect to apply page
        if (!$application) {
            return redirect()->route('seller.apply');
        }

        // If application is approved, redirect to seller dashboard
        if ($application->status === 'approved') {
            return redirect()->route('seller.dashboard');
        }

        // If application is rejected, redirect to rejected page
        if ($application->status === 'rejected') {
            return redirect()->route('seller.rejected');
        }

        // If application is still pending, show the pending page
        return view('seller.pending');
    })->name('seller.pending');
    Route::get('/seller/rejected', function () {
        $user = auth()->user();
        $application = SellerApplication::where('user_id', $user->id)->first();

        // If no application exists, redirect to apply page
        if (!$application) {
            return redirect()->route('seller.apply');
        }

        // If application is approved, redirect to seller dashboard
        if ($application->status === 'approved') {
            return redirect()->route('seller.dashboard');
        }

        // If application is still pending, redirect to pending page
        if ($application->status === 'pending') {
            return redirect()->route('seller.pending');
        }

        // If application is rejected, show the rejected page
        return view('seller.rejected', compact('application'));
    })->name('seller.rejected');

    // Seller Dashboard Features (Placeholders)
    Route::get('/seller/products/create', function () {
        return view('seller.products.create');
    })->name('seller.products.create');

    Route::get('/seller/store', function () {
        return view('seller.store');
    })->name('seller.view-store');

    // Admin Routes for Seller Applications
    Route::get('/admin/seller-applications', [SellerApplicationController::class, 'index'])->name('admin.seller_applications');
    Route::post('/admin/seller-applications/{application}/update-status', [SellerApplicationController::class, 'updateStatus'])->name('admin.seller_applications.update_status');

    // Admin Routes for Product Categories
    Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
        Route::resource('categories', App\Http\Controllers\Admin\ProductCategoryController::class);
        Route::post('categories/update-order', [App\Http\Controllers\Admin\ProductCategoryController::class, 'updateOrder'])->name('categories.update-order');
    });
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::post('/product/cancel-transaction', [App\Http\Controllers\OrderController::class, 'cancelTransaction'])->name('product.cancel-transaction');
    Route::post('/product/check-transaction', [App\Http\Controllers\OrderController::class, 'checkTransaction'])->name('product.check-transaction');
    Route::post('/product/buy', [App\Http\Controllers\OrderController::class, 'store'])->name('product.buy');
    Route::post('/product/download/{product:id}', [App\Http\Controllers\UserController::class, 'productDownload'])->name('product.download');
});

// Seller Dashboard Routes
Route::middleware(['auth', 'seller'])->prefix('seller')->name('seller.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    Route::post('products/{product}/toggle-status', [ProductController::class, 'toggleStatus'])->name('products.toggle-status');

    // Product Routes
    Route::resource('products', ProductController::class);

    // Order Routes
    Route::resource('orders', OrderController::class)->only(['index', 'show', 'update']);
    Route::get('orders/{order}/download-invoice', [OrderController::class, 'downloadInvoice'])->name('orders.downloadInvoice');
    Route::get('orders/export/csv', [OrderController::class, 'exportCsv'])->name('orders.export.csv');
    Route::get('orders/export/pdf', [OrderController::class, 'exportPdf'])->name('orders.export.pdf');

    // Analytics Routes
    Route::get('/analytics', [AnalyticsController::class, 'index'])->name('analytics');
    Route::get('/analytics/export/csv', [AnalyticsController::class, 'exportCsv'])->name('analytics.export.csv');
    Route::get('/analytics/export/pdf', [AnalyticsController::class, 'exportPdf'])->name('analytics.export.pdf');

    // Payments Routes
    Route::resource('payments', PaymentController::class)->only(['index', 'show']);
    Route::get('payments/{payment}/download-receipt', [PaymentController::class, 'downloadReceipt'])->name('payments.downloadReceipt');
    Route::post('payments/update-payment-method', [PaymentController::class, 'updatePaymentMethod'])->name('payments.updatePaymentMethod');

    // Settings Routes
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings');
    Route::post('/settings/profile', [SettingsController::class, 'updateProfile'])->name('settings.updateProfile');
    Route::post('/settings/store', [SettingsController::class, 'updateStore'])->name('settings.updateStore');
    Route::post('/settings/password', [SettingsController::class, 'updatePassword'])->name('settings.updatePassword');
    Route::post('/settings/notifications', [SettingsController::class, 'updateNotifications'])->name('settings.updateNotifications');

    // Help Center Routes
    Route::prefix('help-center')->name('help-center.')->group(function () {
        Route::get('/', [HelpCenterController::class, 'index'])->name('index');
        Route::get('/article/{slug}', [HelpCenterController::class, 'article'])->name('article');
        Route::get('/contact', [HelpCenterController::class, 'contactForm'])->name('contact');
        Route::post('/contact', [HelpCenterController::class, 'submitContact'])->name('submitContact');
    });

    // Documentation Routes
    Route::prefix('documentation')->name('documentation.')->group(function () {
        Route::get('/', [DocumentationController::class, 'index'])->name('index');
        Route::get('/{slug}', [DocumentationController::class, 'show'])->name('show');
        Route::post('/{slug}/feedback', [DocumentationController::class, 'feedback'])->name('feedback');
    });
});

// User Dashboard Routes
Route::middleware(['auth'])->prefix('user')->name('user.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\UserController::class, 'dashboard'])->name('dashboard');
    Route::get('/purchases', [App\Http\Controllers\UserController::class, 'purchases'])->name('purchases');
    Route::get('/profile', [App\Http\Controllers\UserController::class, 'profile'])->name('profile');
    Route::put('/profile', [App\Http\Controllers\UserController::class, 'updateProfile'])->name('profile.update');
    Route::put('/password', [App\Http\Controllers\UserController::class, 'updatePassword'])->name('password.update');
    Route::get('/settings', [App\Http\Controllers\UserController::class, 'settings'])->name('settings');
    Route::put('/settings', [App\Http\Controllers\UserController::class, 'updateSettings'])->name('settings.update');
    Route::get('/seller-dashboard', [App\Http\Controllers\UserController::class, 'sellerDashboard'])->name('seller-dashboard');
});

// Public Browse Products Route
Route::get('/browse', [PagesController::class, 'browseProducts'])->name('user.browse');
Route::get('user/browse-stores', [App\Http\Controllers\UserController::class, 'browseStores'])->name('user.browse-stores');

// Cart Routes
Route::get('/cart', [App\Http\Controllers\CartController::class, 'index'])->name('cart.index');
Route::post('/cart/add', [App\Http\Controllers\CartController::class, 'addToCart'])->name('cart.add');
Route::put('/cart/update/{id}', [App\Http\Controllers\CartController::class, 'updateCartItem'])->name('cart.update');
Route::delete('/cart/remove/{id}', [App\Http\Controllers\CartController::class, 'removeCartItem'])->name('cart.remove');
Route::delete('/cart/clear', [App\Http\Controllers\CartController::class, 'clearCart'])->name('cart.clear');
Route::get('/cart/checkout', [App\Http\Controllers\CartController::class, 'checkout'])->name('cart.checkout');
Route::post('/cart/process-payment', [App\Http\Controllers\CartController::class, 'processPayment'])->name('cart.process-payment');
Route::post('/cart/check-transactions', [App\Http\Controllers\CartController::class, 'checkTransactions'])->name('cart.check-transactions');
Route::post('/cart/cancel-transactions', [App\Http\Controllers\CartController::class, 'cancelTransactions'])->name('cart.cancel-transactions');

Route::get('/products', function () {
    return view('products');
})->name('products');

Route::get('/categories', function () {
    return view('categories');
})->name('categories');

Route::get('/category/{slug}', function ($slug) {
    return view('category', ['slug' => $slug]);
})->name('category');

Route::get('/product/{slug}', function ($slug) {
    return view('product', ['slug' => $slug]);
})->name('product');

Route::get('/sell', function () {
    return view('sell');
})->name('sell');

// Route::get('/about', [PagesController::class, 'aboutUs'])->name('about');
Route::get('/about', function () {
    return redirect('/');
})->name('about');

// Commented out routes that are not needed for now
/*
Route::get('/pricing', function () {
    return view('pricing');
})->name('pricing');

Route::get('/success-stories', [PagesController::class, 'successStories'])->name('success-stories');
Route::get('/blog', [PagesController::class, 'blog'])->name('blog');
Route::get('/careers', [PagesController::class, 'careers'])->name('careers');
Route::get('/contact', [PagesController::class, 'contact'])->name('contact');
Route::post('/contact', [PagesController::class, 'submitContact'])->name('contact.submit');
Route::get('/help', [PagesController::class, 'helpCenter'])->name('help');
Route::get('/help/article/{slug}', [PagesController::class, 'helpArticle'])->name('help.article');

// Alternative routes with explicit controller
Route::get('/terms-of-service', [App\Http\Controllers\LegalController::class, 'terms'])->name('terms.alt');
Route::get('/privacy-policy', [App\Http\Controllers\LegalController::class, 'privacy'])->name('privacy.alt');

Route::get('/faq', [PagesController::class, 'faq'])->name('faq');
*/
Route::get('/terms-of-service', [App\Http\Controllers\LegalController::class, 'terms'])->name('terms.alt');
Route::get('/privacy-policy', [App\Http\Controllers\LegalController::class, 'privacy'])->name('privacy.alt');

// Keep these routes active for basic functionality
// Route::get('/pricing', function () { return redirect('/'); })->name('pricing');
// Route::get('/success-stories', function () { return redirect('/'); })->name('success-stories');
// Route::get('/blog', function () { return redirect('/'); })->name('blog');
// Route::get('/careers', function () { return redirect('/'); })->name('careers');
// Route::get('/contact', function () { return redirect('/'); })->name('contact');
// Route::get('/help', function () { return redirect('/'); })->name('help');
// Route::get('/terms-of-service', function () { return redirect('/'); })->name('terms.alt');
// Route::get('/privacy-policy', function () { return redirect('/'); })->name('privacy.alt');
// Route::get('/faq', function () { return redirect('/'); })->name('faq');

// Store Logo Route
Route::get('/seller/products/{product:slug}/preview', [ProductController::class, 'preview'])->name('products.show');
Route::get('/store-logo/{storeSlug}', [App\Http\Controllers\StoreController::class, 'getStoreLogo'])->name('store.logo');

// Public Store Routes
Route::get('/{sellerApplication:storeNameSlug}', [App\Http\Controllers\StoreController::class, 'show'])->name('store.show');
Route::get('/{sellerApplication:storeNameSlug}/about', [App\Http\Controllers\StoreController::class, 'about'])->name('store.about');
Route::get('/{sellerApplication:storeNameSlug}/all-products', [App\Http\Controllers\StoreController::class, 'allProducts'])->name('store.all-products');
Route::get('/{sellerApplication:storeNameSlug}/category/{category}', [App\Http\Controllers\StoreController::class, 'category'])->name('store.category');
Route::get('/{sellerApplication:storeNameSlug}/search', [App\Http\Controllers\StoreController::class, 'search'])->name('store.search');
Route::get('/{sellerApplication:storeNameSlug}/{product:slug}', [App\Http\Controllers\StoreController::class, 'product'])->name('store.product');

// Route::get('/test-page', function () {
//     return view('test-page');
// })->name('test.page');

// AI Chat Routes
Route::prefix('ai-chat')->name('ai-chat.')->group(function () {
    Route::get('/conversation', [AiChatController::class, 'getConversation'])->name('conversation');
    Route::post('/send-message', [AiChatController::class, 'sendMessage'])->name('send-message');
    Route::post('/clear-conversation', [AiChatController::class, 'clearConversation'])->name('clear-conversation');
});

// Authentication routes are now handled by Laravel Fortify
