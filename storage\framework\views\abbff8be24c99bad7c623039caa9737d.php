<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QWR2LGRD93"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-QWR2LGRD93');
    </script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e($seller->store_name); ?> - Digitora</title>
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('images/digitora-logo.png')); ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- AI Chat CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/ai-chat.css')); ?>">

    
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Scripts -->
    
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <style>
        /* Custom styles for improved design */
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
            --secondary-gradient: linear-gradient(135deg, #4f46e5 0%, #9333ea 100%);
            --primary-color: #6366f1;
            --secondary-color: #a855f7;
            --transition-speed: 0.3s;
        }

        body {
            background-color: #f9fafb;
        }

        /* Header and Navigation Styles */
        .hero-gradient {
            background: var(--primary-gradient);
            box-shadow: 0 4px 15px -3px rgba(99, 102, 241, 0.15);
        }

        .nav-link {
            position: relative;
            transition: all var(--transition-speed) ease;
            padding-bottom: 4px;
            margin-bottom: -4px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-gradient);
            transition: width var(--transition-speed) ease;
            border-radius: 2px;
        }

        .nav-link:hover::after {
            width: 70%;
        }

        .nav-link.active::after {
            width: 70%;
        }

        .nav-link:hover {
            transform: translateY(-2px);
        }

        /* Card Animations */
        .card-hover {
            transition: transform var(--transition-speed) ease,
                box-shadow var(--transition-speed) ease,
                border-color var(--transition-speed) ease;
            border: 1px solid transparent;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.1), 0 10px 20px -10px rgba(0, 0, 0, 0.04);
            border-color: rgba(99, 102, 241, 0.1);
        }

        /* Scroll Animations */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.7s ease, transform 0.7s ease;
        }

        .animate-on-scroll.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Cart Dropdown Styles */
        .cart-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            width: 320px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(10px);
            visibility: hidden;
            transition: opacity var(--transition-speed) ease, transform var(--transition-speed) ease, visibility var(--transition-speed) ease;
            z-index: 50;
            overflow: hidden;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .cart-container:hover .cart-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        /* Mobile Menu Enhancements */
        .mobile-menu-container {
            transition: max-height 0.5s ease;
            max-height: 0;
            overflow: hidden;
        }

        .mobile-menu-container.open {
            max-height: 500px;
        }

        .mobile-menu-item {
            transform: translateX(-10px);
            opacity: 0;
            transition: transform 0.3s ease, opacity 0.3s ease;
            transition-delay: calc(var(--index) * 0.05s);
        }

        .mobile-menu-container.open .mobile-menu-item {
            transform: translateX(0);
            opacity: 1;
        }

        /* Button Styles */
        .btn {
            transition: all var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .btn::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: -100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
            transition: left 0.6s ease;
        }

        .btn:hover::after {
            left: 100%;
        }

        /* Footer Enhancements */
        .footer-link {
            transition: all var(--transition-speed) ease;
            position: relative;
            display: inline-block;
        }

        .footer-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 1px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-color);
            transition: width var(--transition-speed) ease;
        }

        .footer-link:hover::after {
            width: 100%;
        }

        .social-icon {
            transition: all var(--transition-speed) ease;
        }

        .social-icon:hover {
            transform: translateY(-3px);
            color: var(--primary-color);
        }
    </style>
</head>

<body class="font-sans antialiased bg-gray-50">
    <!-- Store Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Store Logo and Name -->
                <div class="flex items-center">
                    <a href="<?php echo e(route('store.show', $seller->store_slug)); ?>" class="flex items-center gap-2">
                        <?php if($seller->store_logo): ?>
                            <img src="<?php echo e(route('store.logo', $seller->store_slug)); ?>" alt="<?php echo e($seller->store_name); ?>"
                                class="h-9 w-9 rounded-lg object-cover shadow-sm">
                        <?php else: ?>
                            <div
                                class="flex h-9 w-9 items-center justify-center rounded-lg hero-gradient text-white shadow-sm">
                                <span class="text-base font-bold"><?php echo e(substr($seller->store_name, 0, 1)); ?></span>
                            </div>
                        <?php endif; ?>
                        <span class="text-base font-bold text-gray-900"><?php echo e($seller->store_name); ?></span>
                    </a>

                    <!-- Edit Store Button (Only visible to store owner) -->
                    <?php if(auth()->guard()->check()): ?>
                        <?php if(Auth::user()->is_seller && Auth::user()->id === $seller->id): ?>
                            <a href="<?php echo e(route('seller.settings')); ?>" class="ml-3 inline-flex items-center rounded-md bg-indigo-50 px-2.5 py-1 text-xs font-medium text-indigo-700 hover:bg-indigo-100 transition-colors duration-200">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                Edit Store
                            </a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex space-x-6">
                    <a href="<?php echo e(route('home')); ?>"
                        class="text-gray-700 hover:text-indigo-600 px-2 py-1 text-sm font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        Home
                    </a>
                    <a href="<?php echo e(route('store.show', $seller->store_slug)); ?>"
                        class="text-gray-700 hover:text-indigo-600 px-2 py-1 text-sm font-medium <?php echo e(request()->routeIs('store.show') && !request()->route('category') ? 'text-indigo-600' : ''); ?>">
                        Store
                    </a>
                    <a href="<?php echo e(route('store.show', $seller->store_slug . '#products')); ?>"
                        class="text-gray-700 hover:text-indigo-600 px-2 py-1 text-sm font-medium <?php echo e(request()->route('category') ? 'text-indigo-600' : ''); ?>">
                        Products
                    </a>
                    <a href="<?php echo e(route('store.about', $seller->store_slug)); ?>"
                        class="text-gray-700 hover:text-indigo-600 px-2 py-1 text-sm font-medium <?php echo e(request()->routeIs('store.about') ? 'text-indigo-600' : ''); ?>">
                        About
                    </a>
                </nav>

                <?php
                    // Define cart count variable
                    $cartCount = 0;
                    if (Auth::check()) {
                        $cart = \App\Models\Cart::where('user_id', Auth::id())->first();
                        if ($cart) {
                            $cartCount = $cart->items->sum('quantity');
                        }
                    } else {
                        $sessionId = session()->get('cart_session_id');
                        if ($sessionId) {
                            $cart = \App\Models\Cart::where('session_id', $sessionId)->first();
                            if ($cart) {
                                $cartCount = $cart->items->sum('quantity');
                            }
                        }
                    }
                ?>

                <!-- Search and Cart -->
                <div class="hidden md:flex items-center space-x-4">
                    <form action="<?php echo e(route('store.search', $seller->store_slug)); ?>" method="GET" class="relative">
                        <input type="text" name="query" placeholder="Search..." required
                            class="w-48 pl-8 pr-3 py-1.5 rounded-lg border border-gray-300 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                        <div class="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
                            <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                    </form>
                    <a href="<?php echo e(route('cart.index')); ?>"
                        class="relative text-gray-700 hover:text-purple-600 transition-colors duration-200 p-1.5"
                        aria-label="View Cart">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <?php if($cartCount > 0): ?>
                            <span
                                class="absolute -top-1 -right-1 bg-purple-600 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs"><?php echo e($cartCount); ?></span>
                        <?php endif; ?>
                    </a>
                </div>

                <!-- Mobile menu button -->
                <div class="flex items-center md:hidden space-x-3">
                    <a href="<?php echo e(route('cart.index')); ?>"
                        class="relative text-gray-700 hover:text-purple-600 transition-colors duration-200 p-1"
                        aria-label="View Cart">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <?php if($cartCount > 0): ?>
                            <span
                                class="absolute -top-1 -right-1 bg-purple-600 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs"><?php echo e($cartCount); ?></span>
                        <?php endif; ?>
                    </a>
                    <button type="button"
                        class="text-gray-500 hover:text-indigo-600 focus:outline-none transition-colors duration-300"
                        x-data="{}"
                        @click="document.getElementById('mobile-menu').classList.toggle('hidden')">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu, show/hide based on menu state. -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="<?php echo e(route('home')); ?>"
                    class="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    Main Site
                </a>
                <a href="<?php echo e(route('cart.index')); ?>"
                    class="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Cart
                    <?php if($cartCount > 0): ?>
                        <span
                            class="ml-2 bg-purple-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"><?php echo e($cartCount); ?></span>
                    <?php endif; ?>
                </a>
                <a href="<?php echo e(route('store.show', $seller->store_slug)); ?>"
                    class="block px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 <?php echo e(request()->routeIs('store.show') && !request()->route('category') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600'); ?>">
                    Store
                </a>
                <a href="<?php echo e(route('store.show', $seller->store_slug . '#products')); ?>"
                    class="block px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 <?php echo e(request()->route('category') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600'); ?>">
                    Products
                </a>
                <a href="<?php echo e(route('store.about', $seller->store_slug)); ?>"
                    class="block px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 <?php echo e(request()->routeIs('store.about') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600'); ?>">
                    About
                </a>

                <!-- Edit Store Button (Only visible to store owner) - Mobile -->
                <?php if(auth()->guard()->check()): ?>
                    <?php if(Auth::user()->is_seller && Auth::user()->id === $seller->id): ?>
                        <a href="<?php echo e(route('seller.settings')); ?>"
                            class="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 text-indigo-600 bg-indigo-50 hover:bg-indigo-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Edit Store
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            <div class="pt-2 pb-3 border-t border-gray-200">
                <div class="px-4 py-2">
                    <form action="<?php echo e(route('store.search', $seller->store_slug)); ?>" method="GET" class="relative">
                        <input type="text" name="query" placeholder="Search products..." required
                            class="w-full pl-9 pr-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Store Footer -->
    <footer class="bg-white border-t mt-8 py-8">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <div class="flex items-center gap-2 group">
                        <?php if($seller->store_logo): ?>
                            <img src="<?php echo e(route('store.logo', $seller->store_slug)); ?>"
                                alt="<?php echo e($seller->store_name); ?>" class="h-10 w-10 rounded-lg object-cover shadow-sm">
                        <?php else: ?>
                            <div
                                class="flex h-10 w-10 items-center justify-center rounded-lg hero-gradient text-white shadow-md">
                                <span class="text-lg font-bold"><?php echo e(substr($seller->store_name, 0, 1)); ?></span>
                            </div>
                        <?php endif; ?>
                        <span class="text-lg font-bold text-gray-900"><?php echo e($seller->store_name); ?></span>
                    </div>
                    <p class="mt-3 text-sm text-gray-600 leading-relaxed">
                        <?php echo e(Str::limit($seller->store_description ?? 'Welcome to our digital products store. We offer high-quality digital products designed to help you succeed.', 120)); ?>

                    </p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-3">Quick Links</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="<?php echo e(route('home')); ?>"
                                class="text-sm text-gray-600 hover:text-indigo-600 transition-colors duration-300 flex items-center">
                                
                                Main Site
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('store.show', $seller->store_slug)); ?>"
                                class="text-sm text-gray-600 hover:text-indigo-600 transition-colors duration-300 flex items-center">
                                
                                Store Home
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('store.show', $seller->store_slug . '#products')); ?>"
                                class="text-sm text-gray-600 hover:text-indigo-600 transition-colors duration-300 flex items-center">
                                
                                Products
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('store.about', $seller->store_slug)); ?>"
                                class="text-sm text-gray-600 hover:text-indigo-600 transition-colors duration-300 flex items-center">
                                
                                About Us
                            </a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">Contact</h3>
                    <ul class="space-y-3">
                        <?php if($seller->email): ?>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500 mr-3"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <span class="text-gray-600"><?php echo e($seller->email); ?></span>
                            </li>
                        <?php endif; ?>
                        <?php if($seller->phone): ?>
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500 mr-3"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <span class="text-gray-600"><?php echo e($seller->phone); ?></span>
                            </li>
                        <?php endif; ?>
                    </ul>

                    <!-- Social Media Links (Commented out as requested) -->
                    
                </div>
            </div>
            
        </div>
    </footer>

    <!-- AI Chat Component -->
    <?php echo $__env->make('components.ai-chat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- AI Chat JS -->
    <script src="<?php echo e(asset(js_path() . '/ai-chat.js')); ?>" defer></script>

    <!-- Animation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Animate elements on scroll
            const animateElements = document.querySelectorAll('.animate-on-scroll');

            function checkScroll() {
                animateElements.forEach(element => {
                    const elementTop = element.getBoundingClientRect().top;
                    const windowHeight = window.innerHeight;

                    if (elementTop < windowHeight * 0.9) {
                        element.classList.add('visible');
                    }
                });
            }

            // Initial check
            checkScroll();

            // Check on scroll
            window.addEventListener('scroll', checkScroll);
        });
    </script>
</body>

</html>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/store/layout.blade.php ENDPATH**/ ?>