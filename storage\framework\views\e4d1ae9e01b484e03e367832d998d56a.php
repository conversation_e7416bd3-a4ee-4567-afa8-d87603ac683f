

<?php $__env->startSection('head'); ?>
    <!-- Page Context Meta Tags for DigiAI -->
    <meta name="store-slug" content="<?php echo e($seller->sellerApplication->store_name_slug); ?>">
    <meta name="store-id" content="<?php echo e($seller->id); ?>">
    <meta name="category-slug" content="<?php echo e($category); ?>">
    <meta name="page-type" content="category">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="py-8 bg-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumbs -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="<?php echo e(route('store.show', $seller->store_slug)); ?>" class="text-gray-500 hover:text-indigo-600 text-sm font-medium transition duration-150 ease-in-out">
                        Home
                    </a>
                </li>

                <?php if(isset($hasNewCategories) && $hasNewCategories): ?>
                    <?php
                        // Try to determine if this is a category, subcategory, or detailed category
                        $categoryObj = \App\Models\ProductCategory::where('slug', $categorySlugForLinks ?? $category)->first();
                        $subcategoryObj = \App\Models\ProductSubcategory::where('slug', $categorySlugForLinks ?? $category)->first();
                        $detailedCategoryObj = \App\Models\ProductDetailedCategory::where('slug', $categorySlugForLinks ?? $category)->first();
                    ?>

                    <?php if($detailedCategoryObj && $detailedCategoryObj->subcategory && $detailedCategoryObj->subcategory->category): ?>
                        <!-- If it's a detailed category, show the full hierarchy -->
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <a href="<?php echo e(route('store.category', [$seller->store_slug, $detailedCategoryObj->subcategory->category->slug])); ?>" class="text-gray-500 hover:text-indigo-600 ml-1 text-sm font-medium transition duration-150 ease-in-out">
                                    <?php echo e($detailedCategoryObj->subcategory->category->name); ?>

                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <a href="<?php echo e(route('store.category', [$seller->store_slug, $detailedCategoryObj->subcategory->slug])); ?>" class="text-gray-500 hover:text-indigo-600 ml-1 text-sm font-medium transition duration-150 ease-in-out">
                                    <?php echo e($detailedCategoryObj->subcategory->name); ?>

                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-1 text-gray-600 text-sm font-medium"><?php echo e($detailedCategoryObj->name); ?></span>
                            </div>
                        </li>
                    <?php elseif($subcategoryObj && $subcategoryObj->category): ?>
                        <!-- If it's a subcategory, show category -> subcategory -->
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <a href="<?php echo e(route('store.category', [$seller->store_slug, $subcategoryObj->category->slug])); ?>" class="text-gray-500 hover:text-indigo-600 ml-1 text-sm font-medium transition duration-150 ease-in-out">
                                    <?php echo e($subcategoryObj->category->name); ?>

                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-1 text-gray-600 text-sm font-medium"><?php echo e($subcategoryObj->name); ?></span>
                            </div>
                        </li>
                    <?php elseif($categoryObj): ?>
                        <!-- If it's a main category, just show the category -->
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-1 text-gray-600 text-sm font-medium"><?php echo e($categoryObj->name); ?></span>
                            </div>
                        </li>
                    <?php else: ?>
                        <!-- Fallback if we can't determine the category type -->
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-1 text-gray-600 text-sm font-medium"><?php echo e($category); ?></span>
                            </div>
                        </li>
                    <?php endif; ?>
                <?php else: ?>
                    <!-- Legacy category system -->
                    <li>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="ml-1 text-gray-600 text-sm font-medium"><?php echo e($category); ?></span>
                        </div>
                    </li>
                <?php endif; ?>
            </ol>
        </nav>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <!-- Sidebar -->
            <div class="md:col-span-1">
                <div class="bg-white rounded-lg border p-4 sticky top-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Categories</h3>

                    <!-- All Products Button -->
                    <a href="<?php echo e(route('store.all-products', $seller->store_slug)); ?>" class="block w-full mb-4 text-center px-4 py-2 border border-indigo-600 rounded-md text-sm font-medium <?php echo e($category === 'all' ? 'bg-indigo-600 text-white' : 'bg-white text-indigo-600 hover:bg-indigo-50'); ?> transition-colors">
                        View All Products
                    </a>

                    <ul class="space-y-2 border-t pt-4">
                        <?php if(isset($hasNewCategories) && $hasNewCategories): ?>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $catSlug => $catName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e(route('store.category', [$seller->store_slug, $catSlug])); ?>" class="text-gray-500 hover:text-indigo-600 text-sm <?php echo e($catName == $category ? 'font-medium text-indigo-600' : ''); ?>">
                                    <?php echo e($catName); ?> <span class="text-gray-400">(<?php echo e($categoryProductCounts[$catSlug]); ?>)</span>
                                </a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e(route('store.category', [$seller->store_slug, $cat])); ?>" class="text-gray-500 hover:text-indigo-600 text-sm <?php echo e($cat == $category ? 'font-medium text-indigo-600' : ''); ?>">
                                    <?php echo e($cat); ?> <span class="text-gray-400">(<?php echo e($categoryProductCounts[$cat]); ?>)</span>
                                </a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </ul>

                    <form action="<?php echo e($category === 'all' ? route('store.all-products', $seller->store_slug) : route('store.category', [$seller->store_slug, $category])); ?>" method="GET" id="filter-form">
                        <div class="mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Price Range</h3>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input id="price-all" name="price_range" value="all" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        <?php echo e(!request('price_range') || request('price_range') == 'all' ? 'checked' : ''); ?>>
                                    <label for="price-all" class="ml-3 text-sm text-gray-600">All Prices</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="price-1" name="price_range" value="0-100000" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        <?php echo e(request('price_range') == '0-100000' ? 'checked' : ''); ?>>
                                    <label for="price-1" class="ml-3 text-sm text-gray-600">Under Rp 100.000</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="price-2" name="price_range" value="100000-500000" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        <?php echo e(request('price_range') == '100000-500000' ? 'checked' : ''); ?>>
                                    <label for="price-2" class="ml-3 text-sm text-gray-600">Rp 100.000 - Rp 500.000</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="price-3" name="price_range" value="500000-1000000" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        <?php echo e(request('price_range') == '500000-1000000' ? 'checked' : ''); ?>>
                                    <label for="price-3" class="ml-3 text-sm text-gray-600">Rp 500.000 - Rp 1.000.000</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="price-4" name="price_range" value="1000000+" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        <?php echo e(request('price_range') == '1000000+' ? 'checked' : ''); ?>>
                                    <label for="price-4" class="ml-3 text-sm text-gray-600">Over Rp 1.000.000</label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Rating</h3>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input id="rating-all" name="rating" value="all" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        <?php echo e(!request('rating') || request('rating') == 'all' ? 'checked' : ''); ?>>
                                    <label for="rating-all" class="ml-3 text-sm text-gray-600">All Ratings</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="rating-4" name="rating" value="4" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        <?php echo e(request('rating') == '4' ? 'checked' : ''); ?>>
                                    <label for="rating-4" class="ml-3 flex items-center text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <?php for($i = 1; $i <= 4; $i++): ?>
                                                <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            <?php endfor; ?>
                                        </div>
                                        <span class="ml-1">& Up</span>
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input id="rating-3" name="rating" value="3" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        <?php echo e(request('rating') == '3' ? 'checked' : ''); ?>>
                                    <label for="rating-3" class="ml-3 flex items-center text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <?php for($i = 1; $i <= 3; $i++): ?>
                                                <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            <?php endfor; ?>
                                            <?php for($i = 1; $i <= 2; $i++): ?>
                                                <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            <?php endfor; ?>
                                        </div>
                                        <span class="ml-1">& Up</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8">
                            <button type="submit" class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors duration-300">
                                Apply Filters
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products -->
            <div class="md:col-span-3">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900"><?php echo e($category === 'all' ? 'All Products' : $category); ?></h1>
                    <p class="text-gray-500 mt-1"><?php echo e($products->total()); ?> products found</p>
                </div>

                <!-- Filters -->
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4 bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <span class="text-sm text-gray-500 mr-2">Sort by:</span>
                        <select name="sort" form="filter-form" class="rounded-md border-gray-300 py-1 pl-2 pr-8 text-sm focus:border-indigo-500 focus:ring-indigo-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="newest" <?php echo e(request('sort') == 'newest' || !request('sort') ? 'selected' : ''); ?>>Newest</option>
                            <option value="price_low" <?php echo e(request('sort') == 'price_low' ? 'selected' : ''); ?>>Price: Low to High</option>
                            <option value="price_high" <?php echo e(request('sort') == 'price_high' ? 'selected' : ''); ?>>Price: High to Low</option>
                            <option value="popular" <?php echo e(request('sort') == 'popular' ? 'selected' : ''); ?>>Most Popular</option>
                        </select>
                    </div>
                </div>

                <!-- Products Grid -->
                <?php if($products->count() > 0): ?>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="group relative card-hover bg-white rounded-xl overflow-hidden shadow-md border border-gray-100 animate-on-scroll" style="animation-delay: <?php echo e($loop->index * 100); ?>ms">
                        <div class="aspect-w-4 aspect-h-3 overflow-hidden bg-gray-100">
                            <a href="<?php echo e(route('store.product', [$seller->store_slug, $product->slug])); ?>">
                                <img src="<?php echo e($product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg')); ?>"
                                    alt="<?php echo e($product->name); ?>"
                                    class="w-full h-full object-center object-cover transition-transform duration-500 group-hover:scale-110">
                            </a>
                            <?php if($product->discount_price): ?>
                            <div class="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
                                SALE <?php echo e(round((($product->price - $product->discount_price) / $product->price) * 100)); ?>% OFF
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-5">
                            <h3 class="text-lg font-semibold text-gray-900">
                                <a href="<?php echo e(route('store.product', [$seller->store_slug, $product->slug])); ?>">
                                    <span aria-hidden="true" class="absolute inset-0"></span>
                                    <?php echo e($product->name); ?>

                                </a>
                            </h3>
                            <p class="mt-1 text-sm text-gray-500">
                                <?php if(isset($hasNewCategories) && $hasNewCategories && $product->productDetailedCategory): ?>
                                    <?php echo e($product->productDetailedCategory->name); ?>

                                <?php elseif(isset($hasNewCategories) && $hasNewCategories && $product->productSubcategory): ?>
                                    <?php echo e($product->productSubcategory->name); ?>

                                <?php elseif(isset($hasNewCategories) && $hasNewCategories && $product->productCategory): ?>
                                    <?php echo e($product->productCategory->name); ?>

                                <?php else: ?>
                                    <?php echo e(ucfirst($product->category)); ?>

                                <?php endif; ?>
                            </p>
                            <div class="mt-2 flex items-center justify-between">
                                <div>
                                    <?php if($product->discount_price): ?>
                                    <p class="text-sm font-medium text-gray-900">Rp <?php echo e(number_format($product->discount_price, 0, ',', '.')); ?></p>
                                    <p class="text-sm text-gray-500 line-through">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></p>
                                    <?php else: ?>
                                    <p class="text-sm font-medium text-gray-900">Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></p>
                                    <?php endif; ?>
                                </div>
                                <div class="flex items-center">
                                    <div class="flex items-center">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <?php if($i <= ($product->average_rating ?? 5)): ?>
                                            <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                            <?php else: ?>
                                            <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    </div>
                                    <span class="text-xs text-gray-500 ml-1">(<?php echo e($product->reviews_count ?? 0); ?>)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-8">
                    <div class="pagination-container">
                        <?php echo e($products->links('vendor.pagination.custom-tailwind')); ?>

                    </div>
                </div>
                <?php else: ?>
                <div class="text-center py-12">
                    <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M20 12H4" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900"><?php echo e($category === 'all' ? 'No products found' : 'No products found in this category'); ?></h3>
                    <p class="mt-1 text-sm text-gray-500"><?php echo e($category === 'all' ? 'Try adjusting your filters or check back later' : 'Try adjusting your filters or check out other categories'); ?></p>
                    <?php if($category !== 'all'): ?>
                    <p class="mt-1 text-xs text-gray-500">You can view all products using the "View All Products" button in the sidebar</p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('store.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/store/category.blade.php ENDPATH**/ ?>